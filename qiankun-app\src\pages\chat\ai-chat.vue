<template>
  <view class="chat-container">
    <view class="chat-header">
      <view class="chat-title-container">
        <uni-icons type="chat-filled" size="24" color="#007AFF"></uni-icons>
        <text class="chat-title">乾坤袋AI助手</text>
      </view>
      <text class="chat-subtitle">{{ conversationId ? '会话ID: ' + conversationId.substring(0, 8) + '...' : '新对话' }}</text>
    </view>

    <scroll-view
      class="message-list"
      scroll-y="true"
      :scroll-into-view="scrollToView"
      :scroll-with-animation="true"
      @scrolltoupper="loadMoreMessages"
      :refresher-enabled="false"
    >
      <view class="loading" v-if="isLoading">
        <text>加载中...</text>
      </view>

      <view class="welcome-message" v-if="messages.length === 0">
        <text class="welcome-title">欢迎使用乾坤袋AI助手</text>
        <text class="welcome-subtitle">您可以向我提问任何问题</text>
      </view>

      <!-- 所有消息按时间顺序显示 -->
      <view
        v-for="(message, index) in messages"
        :key="'msg-'+index"
        :id="'msg-' + index"
        :class="['message-item', message.role === 'user' ? 'user' : 'assistant']"
      >
        <!-- 用户消息 -->
        <template v-if="message && message.role === 'user'">
          <view class="message-header">
            <text class="message-sender user-sender">用户</text>
            <text class="message-time">{{ message.time }}</text>
          </view>
          <view class="message-content user-content">
            <text class="message-text">{{ message.content }}</text>
          </view>
        </template>

        <!-- AI消息 -->
        <template v-else-if="message && message.role === 'assistant'">
          <view class="message-header">
            <text class="message-sender ai-sender">乾坤袋</text>
            <text class="message-time">{{ message.time }}</text>
          </view>
          <view class="message-wrapper">
            <!-- 思考中状态 -->
            <view class="message-content thinking" v-if="message.isThinking">
              <view class="thinking-indicator">
                <text class="thinking-text">思考中</text>
                <view class="thinking-dots">
                  <text class="dot">.</text>
                  <text class="dot">.</text>
                  <text class="dot">.</text>
                </view>
              </view>
            </view>
            <!-- 错误消息 -->
            <view class="message-content error-content" v-else-if="message.isError">
              <text class="error-text">{{ message.content }}</text>
            </view>
            <!-- 正常消息 -->
            <view class="message-content" v-else>
              <rich-text :nodes="renderMarkdown(message.content)" class="markdown-content"></rich-text>
            </view>

            <!-- 消息操作按钮 -->
            <view class="message-actions" v-if="!message.isThinking">
              <uni-icons type="copy" size="18" color="#999" class="action-icon"></uni-icons>
              <uni-icons type="refresh" size="18" color="#999" class="action-icon"></uni-icons>
            </view>
          </view>
        </template>
      </view>

      <view class="message-placeholder" id="message-bottom"></view>
    </scroll-view>

    <view class="input-area">
      <view class="input-box">
        <view class="input-wrapper">
          <textarea
            class="message-input"
            v-model="inputMessage"
            placeholder="根据场景输入你想要问的问题，[DEMO]1.行业数据库2.期货铜3..."
            :disabled="isSending"
            auto-height
            @confirm="sendMessage"
          />
          <view class="input-actions">
            <uni-icons type="paperclip" size="20" color="#999"></uni-icons>
          </view>
        </view>
        <button class="send-btn" @click="sendMessage" :disabled="isSending || !inputMessage.trim()">
          <text class="send-icon">➤</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import * as marked from 'marked';
import { difyApi } from '@/utils/dify-api';

export default {
  data() {
    return {
      messages: [],
      inputMessage: '',
      scrollToView: 'message-bottom',
      isLoading: false,
      isSending: false,
      conversationId: null
    }
  },
  computed: {
    ...mapState('user', ['userInfo'])
  },
  onLoad(options) {
    console.log('AI对话页面加载');

    // 检查是否已登录
    const token = uni.getStorageSync('token');
    if (!token) {
      // 模拟登录状态，实际应用中应该重定向到登录页
      console.log('用户未登录，使用模拟用户信息');

      // 创建模拟用户信息并存储
      const mockUserInfo = {
        id: 'user-' + Date.now(),
        username: '测试用户',
        role: 'user'
      };

      // 存储到本地
      uni.setStorageSync('userInfo', mockUserInfo);
      uni.setStorageSync('token', 'mock-token');

      // 存储到Vuex（如果需要）
      if (this.$store) {
        this.$store.commit('user/setUserInfo', mockUserInfo);
      }
    }

    // 检查是否有会话ID参数
    if (options && options.conversationId) {
      this.conversationId = options.conversationId;
      console.log('打开已有会话:', this.conversationId);
    } else {
      // 创建新的会话ID（实际使用时，会由Dify API返回）
      this.conversationId = this.generateUUID();
      console.log('创建新会话:', this.conversationId);
    }

    // 加载历史消息
    this.loadMessages();

    // 设置页面标题
    uni.setNavigationBarTitle({
      title: '乾坤袋AI助手'
    });

    // 确保输入框不被键盘遮挡
    uni.onKeyboardHeightChange((res) => {
      if (res.height > 0) {
        // 键盘弹出
        this.scrollToBottom();
      }
    });

    // 打印调试信息
    console.log('页面加载完成，消息数组:', this.messages);
  },
  methods: {
    loadMessages() {
      this.isLoading = true;

      // 检查是否有会话ID
      if (this.conversationId) {
        // 加载特定会话的消息
        difyApi.getMessages(this.conversationId)
          .then(response => {
            console.log('加载历史消息:', response);

            if (response.data && response.data.length > 0) {
              // 转换消息格式
              this.messages = response.data.map(msg => ({
                role: msg.role,
                content: msg.content || msg.answer || msg.text || '',
                time: this.formatTime(new Date(msg.created_at))
              }));
            } else {
              // 添加一条欢迎消息
              this.addWelcomeMessage();
            }
          })
          .catch(error => {
            console.error('加载历史消息失败:', error);
            // 添加一条欢迎消息
            this.addWelcomeMessage();
            uni.showToast({
              title: '加载历史消息失败',
              icon: 'none'
            });
          })
          .finally(() => {
            this.isLoading = false;
            this.scrollToBottom();
          });
      } else {
        // 新会话，添加一条欢迎消息
        this.addWelcomeMessage();
        this.isLoading = false;
      }
    },

    addWelcomeMessage() {
      // 添加一条欢迎消息
      this.messages = [{
        role: 'assistant',
        content: '您好，我是乾坤袋AI助手，可以为您提供专业的金融和投资咨询服务。您可以向我咨询有关市场趋势、投资策略、风险管理等方面的问题，我将尽力为您提供准确、有价值的信息和建议。请问有什么可以帮您的吗？',
        time: this.formatTime(new Date())
      }];
    },

    sendMessage() {
      if (!this.inputMessage.trim() || this.isSending) return;

      console.log('发送消息:', this.inputMessage);

      // 创建用户消息对象
      const userMessage = {
        role: 'user',
        content: this.inputMessage,
        time: this.formatTime(new Date())
      };

      console.log('创建用户消息对象:', userMessage);

      // 添加用户消息
      this.messages.push(userMessage);

      console.log('添加用户消息后的消息数组:', this.messages);
      console.log('用户消息数量:', this.messages.filter(msg => msg.role === 'user').length);

      // 清空输入框
      const message = this.inputMessage;
      this.inputMessage = '';

      // 强制更新视图
      this.$forceUpdate();

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();
        console.log('滚动到底部完成');
      });

      // 添加"思考中"的消息
      const now = new Date();
      const thinkingId = 'thinking-' + Date.now();
      this.messages.push({
        id: thinkingId,
        role: 'assistant',
        content: '思考中...',
        time: this.formatTime(now),
        isThinking: true
      });

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();
      });

      // 发送消息到AI
      this.isSending = true;

      // 调用Dify API
      difyApi.sendMessage(message, this.conversationId)
        .then(response => {
          console.log('Dify API响应:', response);

          console.log('Dify API完整响应:', response);

          // 如果是新对话，保存对话ID
          if (!this.conversationId && response.conversation_id) {
            this.conversationId = response.conversation_id;
          }

          // 移除"思考中"的消息
          this.messages = this.messages.filter(msg => msg.id !== thinkingId);

          // 添加AI回复
          const responseTime = new Date();

          // 解析 Dify API 响应
          let content = '';
          if (response.message) {
            content = response.message;
          } else if (response.choices && response.choices.length > 0) {
            content = response.choices[0].message.content;
          } else if (response.answer) {
            content = response.answer;
          } else if (response.text) {
            content = response.text;
          } else if (response.data && response.data.answer) {
            content = response.data.answer;
          } else {
            content = '无回复内容';
          }

          const aiResponse = {
            role: 'assistant',
            content: content,
            time: this.formatTime(responseTime)
          };

          this.messages.push(aiResponse);
        })
        .catch(error => {
          console.error('API调用失败:', error);
          console.error('错误详情:', JSON.stringify(error));

          // 移除"思考中"的消息
          this.messages = this.messages.filter(msg => msg.id !== thinkingId);

          // 添加错误提示消息
          const errorTime = new Date();
          let errorMessage = '';

          // 处理CORS错误
          if (error.errMsg && error.errMsg.includes('cors')) {
            errorMessage = '跨域请求被阻止，请联系管理员配置CORS';
          } else if (error.errMsg && error.errMsg.includes('timeout')) {
            errorMessage = '请求超时，请检查网络连接';
          } else if (error.errMsg && error.errMsg.includes('404')) {
            errorMessage = 'API接口不存在，请检查API配置';
          } else {
            errorMessage = error.message || error.errMsg || '服务器错误请稍后重试';
          }

          const errorResponse = {
            role: 'assistant',
            content: errorMessage,
            time: this.formatTime(errorTime),
            isError: true // 标记为错误消息
          };

          this.messages.push(errorResponse);
        })
        .finally(() => {
          // 保存对话记录
          this.saveConversation();

          this.isSending = false;

          // 滚动到底部
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        });
    },

    generateAIResponse(message) {
      // 模拟AI回复，实际应该调用AI API
      const responses = [
        `您好，我是乾坤袋AI助手。您的问题是关于"${message}"，我来为您解答：\n\n这是一个很好的问题。根据我的理解，这涉及到几个方面：\n\n1. 首先，我们需要考虑...\n2. 其次，重要的是...\n3. 最后，不要忘记...\n\n希望这个回答对您有所帮助！`,
        `感谢您的提问："${message}"\n\n## 分析\n\n这个问题可以从多个角度来看：\n\n* 从技术层面\n* 从业务层面\n* 从用户体验层面\n\n## 建议\n\n我建议您可以尝试以下方法：\n\n\`\`\`\n1. 步骤一：分析需求\n2. 步骤二：制定计划\n3. 步骤三：执行并评估\n\`\`\`\n\n如果您有更多问题，随时可以继续提问！`,
        `关于"${message}"，我有以下见解：\n\n> 这是一个复杂的话题，需要综合考虑多方面因素。\n\n主要包括：\n\n* 第一点：...\n* 第二点：...\n* 第三点：...\n\n您可以参考这篇文章：[相关资料](https://example.com)`
      ];

      // 随机选择一个回复
      return responses[Math.floor(Math.random() * responses.length)];
    },

    renderMarkdown(text) {
      try {
        // 使用marked库将Markdown转换为HTML
        const html = marked.marked(text);
        return html;
      } catch (error) {
        console.error('Markdown渲染错误:', error);
        return text;
      }
    },

    saveConversation() {
      // 保存对话记录到本地存储
      try {
        // 检查是否有消息
        if (!this.messages || this.messages.length === 0) {
          console.log('没有消息可保存');
          return;
        }

        // 检查是否有会话ID
        if (!this.conversationId) {
          console.log('没有会话ID，无法保存');
          return;
        }

        // 获取现有的对话记录
        let conversations = uni.getStorageSync('conversations') || [];

        // 查找当前会话
        const index = conversations.findIndex(c => c.id === this.conversationId);

        // 获取最后一条消息
        const lastMessage = this.messages[this.messages.length - 1];

        // 确保 userInfo 存在
        const userId = this.userInfo?.id || 'anonymous';
        const username = this.userInfo?.username || 'Anonymous User';

        const conversation = {
          id: this.conversationId,
          userId: userId,
          username: username,
          lastMessage: lastMessage.content.substring(0, 50) + (lastMessage.content.length > 50 ? '...' : ''),
          lastTime: new Date().toISOString(),
          messages: this.messages
        };

        if (index !== -1) {
          // 更新现有会话
          conversations[index] = conversation;
        } else {
          // 添加新会话
          conversations.push(conversation);
        }

        // 保存回本地存储
        uni.setStorageSync('conversations', conversations);
        console.log('对话保存成功');
      } catch (error) {
        console.error('保存对话记录失败:', error);
      }
    },

    scrollToBottom() {
      // 使用ID滚动到底部
      this.$nextTick(() => {
        if (this.messages && this.messages.length > 0) {
          this.scrollToView = 'msg-' + (this.messages.length - 1);
          console.log('滚动到消息:', this.scrollToView);
        } else {
          this.scrollToView = 'message-bottom';
          console.log('滚动到底部占位符');
        }

        // 强制更新视图
        this.$forceUpdate();
      });
    },

    loadMoreMessages() {
      // 加载更多历史消息
      console.log('加载更多历史消息');
      // 由于是模拟，我们只显示一个提示
      uni.showToast({
        title: '没有更多历史消息',
        icon: 'none'
      });
    },

    formatTime(date) {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    },

    generateUUID() {
      // 生成简单的UUID
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    },

    // 添加测试消息
    addTestMessages() {
      console.log('添加测试消息');

      // 清空现有消息
      this.messages = [];

      const now = new Date();
      const time1 = new Date(now.getTime() - 3600000); // 1小时前
      const time2 = new Date(now.getTime() - 3590000); // 59分50秒前
      const time3 = new Date(now.getTime() - 1800000); // 30分钟前
      const time4 = new Date(now.getTime() - 1790000); // 29分50秒前

      // 添加测试消息
      this.messages.push({
        role: 'user',
        content: '你好，请介绍一下自己',
        time: this.formatTime(time1)
      });

      this.messages.push({
        role: 'assistant',
        content: '您好，我是乾坤袋AI助手，一个基于人工智能的对话系统。我可以回答您的问题，提供信息，或者与您进行日常对话。有什么我可以帮您的吗？',
        time: this.formatTime(time2)
      });

      this.messages.push({
        role: 'user',
        content: '今天天气怎么样？',
        time: this.formatTime(time3)
      });

      this.messages.push({
        role: 'assistant',
        content: '我无法获取实时的天气信息，因为我没有访问互联网的能力。您可以通过查看天气应用、网站或者看窗外来了解今天的天气状况。如果您告诉我您所在的位置，我可以提供一些关于该地区典型天气模式的一般信息。',
        time: this.formatTime(time4)
      });

      // 强制更新视图
      this.$forceUpdate();

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();
      });

      console.log('测试消息添加完成，消息数组:', this.messages);
      console.log('用户消息数量:', this.messages.filter(msg => msg.role === 'user').length);
      console.log('AI消息数量:', this.messages.filter(msg => msg.role === 'assistant').length);
    },

    // 此方法已被真实的Dify API调用替代
  }
}
</script>

<style>
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f9f9f9;
  position: relative;
  box-sizing: border-box;
  padding-top: var(--status-bar-height); /* 适配顶部状态栏 */
  padding-bottom: 0;
}

.chat-header {
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #eaeaea;
  background-color: #fff;
}

.chat-title-container {
  display: flex;
  align-items: center;
  margin-bottom: 6rpx;
}

.chat-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-left: 10rpx;
}

.chat-subtitle {
  font-size: 26rpx;
  color: #999;
  margin-top: 6rpx;
  display: block;
}

.message-list {
  flex: 1;
  padding: 20rpx 30rpx;
  padding-bottom: 300rpx; /* 为底部输入框和导航栏留出更多空间 */
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
  display: flex;
  flex-direction: column;
}

/* 只在内容超出时显示滚动条 */
.message-list::-webkit-scrollbar {
  width: 4px;
}

.message-list::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.loading {
  text-align: center;
  padding: 20rpx 0;
}

.loading text {
  font-size: 24rpx;
  color: #999;
}

.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.welcome-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.welcome-subtitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.test-btn {
  margin-top: 20rpx;
  background-color: #007AFF;
  color: #fff;
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  border-radius: 10rpx;
}

.message-item {
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
  position: relative;
  max-width: 90%;
}

.message-item.user {
  align-self: flex-end;
}

.message-item.assistant {
  align-self: flex-start;
}

.message-header {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
  padding: 0 10rpx;
}

.message-sender {
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 10rpx;
}

.user-sender {
  color: #007AFF;
}

.ai-sender {
  color: #333333;
}

.message-time {
  font-size: 22rpx;
  color: #999;
}

.message-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.message-content {
  background-color: #f9f9f9;
  border-radius: 10rpx;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
  margin-bottom: 10rpx;
  width: 100%;
  box-sizing: border-box;
}

.user-content {
  background-color: #e1f3ff;
  border-radius: 20rpx;
  align-self: flex-end;
}

.message-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  word-break: break-word;
}

.error-content {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
}

.error-text {
  color: #f5222d;
  font-size: 28rpx;
}

.thinking {
  background-color: #f5f5f5;
}

.thinking-indicator {
  display: flex;
  flex-direction: column;
}

.thinking-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.thinking-dots {
  display: flex;
}

.dot {
  font-size: 32rpx;
  color: #666;
  animation: dotAnimation 1.5s infinite;
  margin-right: 5rpx;
}

.dot:nth-child(2) {
  animation-delay: 0.5s;
}

.dot:nth-child(3) {
  animation-delay: 1s;
}

@keyframes dotAnimation {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
}

.message-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10rpx;
}

.action-icon {
  margin-left: 20rpx;
  font-size: 32rpx;
  color: #999;
}

.markdown-content {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  width: 100%;
  display: block;
  overflow-x: auto;
}

/* 为Markdown内容添加样式 */
.markdown-content >>> h1,
.markdown-content >>> h2,
.markdown-content >>> h3,
.markdown-content >>> h4,
.markdown-content >>> h5 {
  margin-top: 20rpx;
  margin-bottom: 10rpx;
  font-weight: bold;
}

.markdown-content >>> h1 {
  font-size: 36rpx;
}

.markdown-content >>> h2 {
  font-size: 34rpx;
}

.markdown-content >>> h3 {
  font-size: 32rpx;
}

.markdown-content >>> p {
  margin: 16rpx 0;
}

.markdown-content >>> ul,
.markdown-content >>> ol {
  padding-left: 40rpx;
  margin: 16rpx 0;
}

.markdown-content >>> li {
  margin: 8rpx 0;
}

.markdown-content >>> code {
  background-color: #f5f5f5;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-family: monospace;
}

.markdown-content >>> pre {
  background-color: #f5f5f5;
  padding: 20rpx;
  border-radius: 8rpx;
  overflow-x: auto;
  margin: 16rpx 0;
}

.message-text {
  font-size: 30rpx;
  color: #333;
}

.error-bubble {
  background-color: #fff2f0 !important;
  border: 1px solid #ffccc7;
}

.error-text {
  color: #ff4d4f !important;
}
table{
  width: max-content;

}
.message-time {
  font-size: 24rpx;
  color: #999;
  margin-left: 20rpx;
  margin-right: 20rpx;
}

.user .message-time {
  text-align: right;
}

.message-placeholder {
  height: 150rpx;
  padding-bottom: 100rpx;
}

.input-area {
  padding: 15rpx;
  background-color: #fff;
  border-top: 1px solid #eaeaea;
  position: fixed;
  bottom: 120rpx; /* 为底部导航栏留出更多空间 */
  left: 0;
  right: 0;
  z-index: 9999; /* 提高z-index确保在最上层 */
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05); /* 添加阴影增强视觉分离 */
}

.input-box {
  display: flex;
  align-items: flex-end;
}

.input-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  margin-right: 15rpx;
  padding: 15rpx;
}

.message-input {
  width: 100%;
  min-height: 60rpx;
  max-height: 200rpx;
  font-size: 28rpx;
  background-color: transparent;
  padding: 0;
}

.input-actions {
  display: flex;
  justify-content: flex-start;
  margin-top: 10rpx;
}

.input-action-icon {
  font-size: 32rpx;
  color: #999;
  margin-right: 20rpx;
}

.send-btn {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #1976D2, #64B5F6);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  box-shadow: 0 4rpx 10rpx rgba(0, 122, 255, 0.3);
  border: none;
}

.send-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 5rpx rgba(0, 122, 255, 0.2);
}

.send-btn[disabled] {
  background: #cccccc;
  box-shadow: none;
}

.send-icon {
  font-size: 36rpx;
  color: #fff;
  font-weight: bold;
}
</style>
