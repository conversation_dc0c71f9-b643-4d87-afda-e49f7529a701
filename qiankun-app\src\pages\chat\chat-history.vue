<template>
  <view class="history-container">
    <!-- 用户列表视图 -->
    <view v-if="currentView === 'userList'" class="user-list-view">
      <!-- 搜索栏 -->
      <view class="search-bar" v-if="canViewHistory">
        <input
          class="search-input"
          type="text"
          placeholder="搜索用户名..."
          v-model="searchKeyword"
          @input="handleSearch"
        />
        <text class="search-icon">🔍</text>
      </view>

      <!-- 用户列表 -->
      <view class="user-list">
        <view
          class="user-item"
          v-for="user in filteredUsers"
          :key="user.id"
          @click="viewUserConversations(user)"
        >
          <view class="user-avatar">
            <image
              v-if="user.picture"
              :src="user.picture"
              class="avatar-image"
              mode="aspectFill"
            ></image>
            <text v-else class="avatar-text">{{ user.name ? user.name.substring(0, 1) : '用' }}</text>
          </view>

          <view class="user-content">
            <view class="user-header">
              <text class="user-name">{{ user.name || '未知用户' }}</text>
              <view class="user-level-badge" :class="getLevelClass(user.level)">
                <text class="level-text">{{ getLevelName(user.level) }}</text>
              </view>
            </view>

            <view class="user-info">
              <text class="user-last-message">最新对话: {{ user.lastQuestion || '暂无对话' }}</text>
              <text class="user-conversation-count">对话数: {{ user.conversationCount || 0 }}</text>
              <text class="user-last-time">{{ formatDate(user.lastDate) }}</text>
            </view>
          </view>

          <view class="user-action">
            <text class="action-icon">→</text>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="!isLoading && filteredUsers.length === 0">
          <text class="empty-icon">💬</text>
          <text class="empty-text">暂无对话记录</text>
          <text class="empty-subtext" v-if="!canViewHistory">您没有权限查看历史记录</text>
          <text class="empty-subtext" v-else>还没有用户进行过对话</text>
        </view>

        <!-- 加载状态 -->
        <view class="loading-state" v-if="isLoading">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载中...</text>
        </view>
      </view>
    </view>

    <!-- 对话详情视图 -->
    <view v-else-if="currentView === 'chatDetail'" class="chat-detail-view">
      <!-- 顶部导航 -->
      <view class="chat-header">
        <view class="back-button" @click="backToUserList">
          <text class="back-icon">←</text>
        </view>
        <view class="chat-user-info">
          <view class="chat-avatar">
            <image
              v-if="selectedUser.picture"
              :src="selectedUser.picture"
              class="avatar-image"
              mode="aspectFill"
            ></image>
            <text v-else class="avatar-text">{{ selectedUser.name ? selectedUser.name.substring(0, 1) : '用' }}</text>
          </view>
          <view class="chat-user-details">
            <text class="chat-user-name">{{ selectedUser.name || '未知用户' }}</text>
            <text class="chat-user-level">{{ getLevelName(selectedUser.level) }}</text>
          </view>
        </view>
      </view>

      <!-- 对话列表 -->
      <view class="chat-messages">
        <view
          class="message-item"
          v-for="(message, index) in userChatHistory"
          :key="message.talk_id || index"
        >
          <view class="message-time">
            <text class="time-text">{{ formatDate(message.date) }}</text>
          </view>

          <!-- 用户问题 -->
          <view class="message-bubble user-message">
            <view class="message-header">
              <text class="message-label">用户提问</text>
            </view>
            <text class="message-text">{{ message.question }}</text>
          </view>

          <!-- AI回答 -->
          <view class="message-bubble ai-message">
            <view class="message-header">
              <text class="message-label">AI回答</text>
            </view>
            <text class="message-text">{{ message.answer }}</text>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-chat-state" v-if="!isLoadingChat && userChatHistory.length === 0">
          <text class="empty-icon">💭</text>
          <text class="empty-text">暂无对话记录</text>
          <text class="empty-subtext">该用户还没有进行过对话</text>
        </view>

        <!-- 加载状态 -->
        <view class="loading-state" v-if="isLoadingChat">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载对话中...</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import { userApi } from '@/utils/api.js';
import { getUserInfo } from '@/utils/auth.js';

export default {
  data() {
    return {
      currentView: 'userList', // 'userList' | 'chatDetail'
      searchKeyword: '',
      isLoading: false,
      isLoadingChat: false,

      // 原始对话数据
      allTalksData: [],

      // 用户列表数据
      userList: [],

      // 选中的用户
      selectedUser: {},

      // 当前用户的对话历史
      userChatHistory: [],

      // 当前用户信息
      currentUser: {}
    }
  },
  computed: {
    ...mapState('user', ['userInfo']),

    // 是否可以查看历史记录
    canViewHistory() {
      return this.currentUser && (this.currentUser.role === 'admin' || this.currentUser.role === 'manager');
    },

    // 过滤后的用户列表
    filteredUsers() {
      if (!this.searchKeyword) {
        return this.userList;
      }

      return this.userList.filter(user => {
        return user.name && user.name.toLowerCase().includes(this.searchKeyword.toLowerCase());
      });
    }
  },
  onLoad() {
    // 获取当前用户信息
    this.currentUser = getUserInfo();

    // 检查是否已登录
    if (!this.currentUser || !this.currentUser.id) {
      uni.redirectTo({
        url: '/pages/auth/login'
      });
      return;
    }

    // 检查权限
    if (!this.canViewHistory) {
      uni.showToast({
        title: '您没有权限查看历史记录',
        icon: 'none'
      });
      return;
    }

    // 加载对话数据
    this.loadTalksData();
  },
  onShow() {
    // 每次页面显示时刷新数据
    if (this.canViewHistory) {
      this.loadTalksData();
    }
  },
  methods: {
    // 加载对话数据
    async loadTalksData() {
      this.isLoading = true;

      try {
        console.log('开始加载对话数据，当前用户ID:', this.currentUser.id);

        // 调用GetTalks接口
        const response = await userApi.getTalks(this.currentUser.id);

        console.log('GetTalks响应:', response);

        if (response.code === 200 && response.data) {
          this.allTalksData = response.data;
          this.processUserList();
        } else if (response.code === 203) {
          // 超级管理员没有权限查看
          uni.showToast({
            title: '超级管理员无法查看对话记录',
            icon: 'none'
          });
        } else if (response.code === 204) {
          // 没有数据
          this.allTalksData = [];
          this.userList = [];
        } else {
          throw new Error('获取对话数据失败');
        }
      } catch (error) {
        console.error('加载对话数据失败:', error);

        uni.showToast({
          title: error.message || '加载失败',
          icon: 'none'
        });

        this.allTalksData = [];
        this.userList = [];
      } finally {
        this.isLoading = false;
      }
    },

    // 处理用户列表
    processUserList() {
      const userMap = new Map();

      // 遍历所有对话数据，按用户分组
      this.allTalksData.forEach(talk => {
        const userId = talk.id;

        if (!userMap.has(userId)) {
          userMap.set(userId, {
            id: userId,
            name: talk.name,
            level: talk.level,
            picture: talk.picture,
            conversations: [],
            conversationCount: 0,
            lastDate: null,
            lastQuestion: null
          });
        }

        const user = userMap.get(userId);
        user.conversations.push(talk);
        user.conversationCount++;

        // 更新最新的对话时间和问题
        if (!user.lastDate || new Date(talk.date) > new Date(user.lastDate)) {
          user.lastDate = talk.date;
          user.lastQuestion = talk.question;
        }
      });

      // 转换为数组并按最新对话时间排序
      this.userList = Array.from(userMap.values()).sort((a, b) => {
        if (!a.lastDate) return 1;
        if (!b.lastDate) return -1;
        return new Date(b.lastDate) - new Date(a.lastDate);
      });

      console.log('处理后的用户列表:', this.userList);
    },

    // 查看用户对话
    viewUserConversations(user) {
      console.log('查看用户对话:', user);

      this.selectedUser = user;
      this.currentView = 'chatDetail';

      // 获取该用户的对话历史
      this.userChatHistory = user.conversations.sort((a, b) => {
        return new Date(b.date) - new Date(a.date);
      });

      console.log('用户对话历史:', this.userChatHistory);
    },

    // 返回用户列表
    backToUserList() {
      this.currentView = 'userList';
      this.selectedUser = {};
      this.userChatHistory = [];
    },

    // 搜索处理
    handleSearch() {
      // 搜索功能已通过计算属性实现
    },

    // 获取级别样式类
    getLevelClass(level) {
      switch (level) {
        case 1:
          return 'level-admin';
        case 2:
          return 'level-manager';
        case 3:
        default:
          return 'level-user';
      }
    },

    // 获取级别名称
    getLevelName(level) {
      switch (level) {
        case 1:
          return '超级管理员';
        case 2:
          return '客户经理';
        case 3:
        default:
          return '普通用户';
      }
    },

    formatDate(dateString) {
      const date = new Date(dateString);
      const now = new Date();

      // 如果是今天的消息，只显示时间
      if (date.toDateString() === now.toDateString()) {
        return this.formatTime(date);
      }

      // 如果是昨天的消息，显示"昨天"
      const yesterday = new Date(now);
      yesterday.setDate(now.getDate() - 1);
      if (date.toDateString() === yesterday.toDateString()) {
        return '昨天 ' + this.formatTime(date);
      }

      // 如果是今年的消息，显示月日
      if (date.getFullYear() === now.getFullYear()) {
        return `${date.getMonth() + 1}月${date.getDate()}日`;
      }

      // 其他情况显示完整日期
      return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;
    },

    formatTime(date) {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    }
  }
}
</script>

<style>
.history-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
}

/* 用户列表视图 */
.user-list-view {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.search-bar {
  padding: 20rpx;
  background-color: #fff;
  position: relative;
}

.search-input {
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  padding: 0 80rpx 0 30rpx;
  font-size: 28rpx;
  width: 100%;
  box-sizing: border-box;
}

.search-icon {
  position: absolute;
  right: 40rpx;
  top: 40rpx;
  font-size: 32rpx;
  color: #999;
}

.user-list {
  flex: 1;
  padding: 0 0 20rpx 0;
}

.user-item {
  display: flex;
  padding: 30rpx;
  background-color: #fff;
  border-bottom: 1px solid #f5f5f5;
  align-items: center;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #007AFF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  flex-shrink: 0;
  overflow: hidden;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

.user-content {
  flex: 1;
  overflow: hidden;
}

.user-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.user-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-right: 15rpx;
}

.user-role {
  font-size: 22rpx;
  color: #fff;
  background-color: #52c41a;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.user-role.manager {
  background-color: #1890ff;
}

.user-role.admin {
  background-color: #722ed1;
}

.user-info {
  display: flex;
  font-size: 24rpx;
  color: #999;
}

.user-last-active {
  margin-right: 20rpx;
}

.user-action {
  padding: 0 10rpx;
}

.action-icon {
  font-size: 36rpx;
  color: #999;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.empty-subtext {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

/* 用户级别徽章 */
.user-level-badge {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  margin-left: 15rpx;
}

.level-admin {
  background-color: #FF9500;
  color: #fff;
}

.level-manager {
  background-color: #007AFF;
  color: #fff;
}

.level-user {
  background-color: #4CD964;
  color: #fff;
}

.level-text {
  font-size: 20rpx;
}

/* 对话详情视图 */
.chat-detail-view {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
}

.chat-header {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e5e5e5;
}

.back-button {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.back-icon {
  font-size: 36rpx;
  color: #007AFF;
}

.chat-user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.chat-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #007AFF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15rpx;
  overflow: hidden;
}

.chat-user-details {
  flex: 1;
}

.chat-user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}

.chat-user-level {
  font-size: 24rpx;
  color: #666;
}

/* 对话消息 */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20rpx;
}

.message-item {
  margin-bottom: 40rpx;
}

.message-time {
  text-align: center;
  margin-bottom: 20rpx;
}

.time-text {
  font-size: 24rpx;
  color: #999;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.message-bubble {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.user-message {
  margin-left: 60rpx;
  background-color: #E3F2FD;
}

.ai-message {
  margin-right: 60rpx;
  background-color: #F5F5F5;
}

.message-header {
  margin-bottom: 15rpx;
}

.message-label {
  font-size: 24rpx;
  color: #666;
  font-weight: bold;
}

.message-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  word-break: break-word;
}

/* 空状态 */
.empty-chat-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-chat-state .empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-chat-state .empty-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.empty-chat-state .empty-subtext {
  font-size: 26rpx;
  color: #666;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}
</style>
