<template>
  <view class="history-container">
    <view class="search-bar" v-if="userInfo.role !== 'user'">
      <input
        class="search-input"
        type="text"
        placeholder="搜索用户名..."
        v-model="searchKeyword"
        @input="handleSearch"
      />
      <text class="search-icon">🔍</text>
    </view>

    <view class="user-list">
      <view
        class="user-item"
        v-for="(user, index) in filteredUsers"
        :key="index"
        @click="viewUserConversations(user)"
      >
        <view class="user-avatar">
          <text class="avatar-text">{{ user.username.substring(0, 1) }}</text>
        </view>

        <view class="user-content">
          <view class="user-header">
            <text class="user-name">{{ user.username }}</text>
            <text class="user-role" v-if="user.role === 'user'">普通用户</text>
            <text class="user-role manager" v-else-if="user.role === 'manager'">客户经理</text>
            <text class="user-role admin" v-else>管理员</text>
          </view>

          <view class="user-info">
            <text class="user-last-active">最近活跃: {{ user.lastActive ? formatDate(user.lastActive) : '从未登录' }}</text>
            <text class="user-conversation-count">对话数: {{ user.conversationCount || 0 }}</text>
          </view>
        </view>

        <view class="user-action">
          <text class="action-icon">→</text>
        </view>
      </view>

      <view class="empty-state" v-if="filteredUsers.length === 0">
        <image class="empty-icon" src="/static/images/empty-chat.png"></image>
        <text class="empty-text">暂无用户</text>
        <text class="empty-subtext" v-if="userInfo.role === 'user'">您没有权限查看其他用户</text>
        <text class="empty-subtext" v-else>您可以在用户管理页面创建新用户</text>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex';

export default {
  data() {
    return {
      users: [],
      searchKeyword: '',
      isLoading: false
    }
  },
  computed: {
    ...mapState('user', ['userInfo']),

    filteredUsers() {
      if (!this.searchKeyword) {
        return this.users;
      }

      return this.users.filter(user => {
        return user.username.toLowerCase().includes(this.searchKeyword.toLowerCase());
      });
    }
  },
  onLoad() {
    // 检查是否已登录
    const token = uni.getStorageSync('token');
    if (!token) {
      uni.redirectTo({
        url: '/pages/auth/login'
      });
      return;
    }

    // 加载用户列表
    this.loadUsers();
  },
  onShow() {
    // 每次页面显示时刷新用户列表
    this.loadUsers();
  },
  methods: {
    loadUsers() {
      this.isLoading = true;

      // 从本地存储加载用户列表
      try {
        // 获取所有用户
        let allUsers = uni.getStorageSync('users') || [];

        // 获取所有对话记录
        let conversations = uni.getStorageSync('conversations') || [];

        // 根据用户角色过滤用户列表
        if (this.userInfo.role === 'user') {
          // 普通用户只能看到自己
          allUsers = allUsers.filter(u => u.id === this.userInfo.id);
        } else if (this.userInfo.role === 'manager') {
          // 客户经理可以看到自己创建的用户
          // 这里假设客户经理创建的用户ID以manager开头
          allUsers = allUsers.filter(u => u.id.startsWith('manager') || u.id === this.userInfo.id);
        }
        // 超级管理员可以看到所有用户

        // 为每个用户添加对话数量和最后活跃时间
        this.users = allUsers.map(user => {
          // 获取该用户的所有对话
          const userConversations = conversations.filter(c => c.userId === user.id);

          // 获取最后活跃时间（最近一次对话的时间）
          let lastActive = null;
          if (userConversations.length > 0) {
            // 按时间排序
            userConversations.sort((a, b) => new Date(b.lastTime) - new Date(a.lastTime));
            lastActive = userConversations[0].lastTime;
          }

          return {
            ...user,
            conversationCount: userConversations.length,
            lastActive: lastActive
          };
        });

        // 按最后活跃时间排序
        this.users.sort((a, b) => {
          if (!a.lastActive) return 1;
          if (!b.lastActive) return -1;
          return new Date(b.lastActive) - new Date(a.lastActive);
        });
      } catch (error) {
        console.error('加载用户列表失败:', error);
        this.users = [];
      }

      this.isLoading = false;
    },

    handleSearch() {
      // 搜索功能已通过计算属性实现
    },

    viewUserConversations(user) {
      // 跳转到用户对话列表页
      uni.navigateTo({
        url: `/pages/chat/user-conversations?userId=${user.id}&username=${user.username}`
      });
    },

    formatDate(dateString) {
      const date = new Date(dateString);
      const now = new Date();

      // 如果是今天的消息，只显示时间
      if (date.toDateString() === now.toDateString()) {
        return this.formatTime(date);
      }

      // 如果是昨天的消息，显示"昨天"
      const yesterday = new Date(now);
      yesterday.setDate(now.getDate() - 1);
      if (date.toDateString() === yesterday.toDateString()) {
        return '昨天 ' + this.formatTime(date);
      }

      // 如果是今年的消息，显示月日
      if (date.getFullYear() === now.getFullYear()) {
        return `${date.getMonth() + 1}月${date.getDate()}日`;
      }

      // 其他情况显示完整日期
      return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;
    },

    formatTime(date) {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    }
  }
}
</script>

<style>
.history-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
}

.search-bar {
  padding: 20rpx;
  background-color: #fff;
  position: relative;
}

.search-input {
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  padding: 0 80rpx 0 30rpx;
  font-size: 28rpx;
  width: 100%;
  box-sizing: border-box;
}

.search-icon {
  position: absolute;
  right: 40rpx;
  top: 40rpx;
  font-size: 32rpx;
  color: #999;
}

.user-list {
  flex: 1;
  padding: 0 0 20rpx 0;
}

.user-item {
  display: flex;
  padding: 30rpx;
  background-color: #fff;
  border-bottom: 1px solid #f5f5f5;
  align-items: center;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #007AFF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.avatar-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

.user-content {
  flex: 1;
  overflow: hidden;
}

.user-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.user-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-right: 15rpx;
}

.user-role {
  font-size: 22rpx;
  color: #fff;
  background-color: #52c41a;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.user-role.manager {
  background-color: #1890ff;
}

.user-role.admin {
  background-color: #722ed1;
}

.user-info {
  display: flex;
  font-size: 24rpx;
  color: #999;
}

.user-last-active {
  margin-right: 20rpx;
}

.user-action {
  padding: 0 10rpx;
}

.action-icon {
  font-size: 36rpx;
  color: #999;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.empty-subtext {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}
</style>
