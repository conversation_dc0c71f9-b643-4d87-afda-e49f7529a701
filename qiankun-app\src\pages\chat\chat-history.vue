<template>
  <view class="history-container">
    <!-- 用户列表视图 -->
    <view v-if="currentView === 'userList'" class="user-list-view">
      <!-- 搜索栏 -->
      <view class="search-bar" v-if="canViewHistory">
        <input
          class="search-input"
          type="text"
          placeholder="搜索用户名..."
          v-model="searchKeyword"
          @input="handleSearch"
        />
        <text class="search-icon">🔍</text>
      </view>

      <!-- 用户列表 -->
      <view class="user-list">
        <view
          class="user-item"
          v-for="user in filteredUsers"
          :key="user.id"
          @click="viewUserConversations(user)"
        >
          <!-- 左侧头像区域 -->
          <view class="user-avatar-container">
            <view class="user-avatar" :class="getLevelClass(user.level)">
              <image
                v-if="user.picture"
                :src="user.picture"
                class="avatar-image"
                mode="aspectFill"
              ></image>
              <text v-else class="avatar-text">{{ user.name ? user.name.substring(0, 1) : '用' }}</text>
            </view>
            <!-- 在线状态指示器 -->
            <view class="online-indicator" :class="{ 'active': user.isOnline }"></view>
          </view>

          <!-- 中间内容区域 -->
          <view class="user-content">
            <view class="user-header">
              <view class="user-name-section">
                <text class="user-name">{{ user.name || '未知用户' }}</text>
                <view class="user-level-badge" :class="getLevelClass(user.level)">
                  <text class="level-icon">{{ getLevelIcon(user.level) }}</text>
                  <text class="level-text">{{ getLevelName(user.level) }}</text>
                </view>
              </view>
              <view class="user-stats">
                <view class="stat-item">
                  <text class="stat-number">{{ user.conversationCount || 0 }}</text>
                  <text class="stat-label">对话</text>
                </view>
              </view>
            </view>

            <view class="user-info">
              <view class="last-message-container">
                <text class="message-label">最新:</text>
                <text class="user-last-message">{{ truncateMessage(user.lastQuestion) || '暂无对话' }}</text>
              </view>
              <view class="time-container">
                <text class="time-icon">🕒</text>
                <text class="user-last-time">{{ formatDate(user.lastDate) }}</text>
              </view>
            </view>
          </view>

          <!-- 右侧操作区域 -->
          <view class="user-action">
            <view class="action-button">
              <text class="action-icon">›</text>
            </view>
            <view class="conversation-badge" v-if="user.conversationCount > 0">
              <text class="badge-text">{{ user.conversationCount > 99 ? '99+' : user.conversationCount }}</text>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="!isLoading && filteredUsers.length === 0">
          <text class="empty-icon">💬</text>
          <text class="empty-text">暂无对话记录</text>
          <text class="empty-subtext" v-if="!canViewHistory">您没有权限查看历史记录</text>
          <text class="empty-subtext" v-else>还没有用户进行过对话</text>
        </view>

        <!-- 加载状态 -->
        <view class="loading-state" v-if="isLoading">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载中...</text>
        </view>
      </view>
    </view>

    <!-- 对话详情视图 -->
    <view v-else-if="currentView === 'chatDetail'" class="chat-detail-view">
      <!-- 顶部导航 -->
      <view class="chat-header">
        <view class="back-button" @click="backToUserList">
          <text class="back-icon">←</text>
        </view>
        <view class="chat-user-info">
          <view class="chat-avatar">
            <image
              v-if="selectedUser.picture"
              :src="selectedUser.picture"
              class="avatar-image"
              mode="aspectFill"
            ></image>
            <text v-else class="avatar-text">{{ selectedUser.name ? selectedUser.name.substring(0, 1) : '用' }}</text>
          </view>
          <view class="chat-user-details">
            <text class="chat-user-name">{{ selectedUser.name || '未知用户' }}</text>
            <text class="chat-user-level">{{ getLevelName(selectedUser.level) }}</text>
          </view>
        </view>
      </view>

      <!-- 对话列表 -->
      <view class="chat-messages">
        <view
          class="message-item"
          v-for="(message, index) in userChatHistory"
          :key="message.talk_id || index"
        >
          <view class="message-time">
            <text class="time-text">{{ formatDate(message.date) }}</text>
          </view>

          <!-- 用户问题 -->
          <view class="message-bubble user-message">
            <view class="message-header">
              <text class="message-label">用户提问</text>
            </view>
            <text class="message-text">{{ message.question }}</text>
          </view>

          <!-- AI回答 -->
          <view class="message-bubble ai-message">
            <view class="message-header">
              <text class="message-label">AI回答</text>
            </view>
            <text class="message-text">{{ message.answer }}</text>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-chat-state" v-if="!isLoadingChat && userChatHistory.length === 0">
          <text class="empty-icon">💭</text>
          <text class="empty-text">暂无对话记录</text>
          <text class="empty-subtext">该用户还没有进行过对话</text>
        </view>

        <!-- 加载状态 -->
        <view class="loading-state" v-if="isLoadingChat">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载对话中...</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import { userApi } from '@/utils/api.js';
import { getUserInfo } from '@/utils/auth.js';

export default {
  data() {
    return {
      currentView: 'userList', // 'userList' | 'chatDetail'
      searchKeyword: '',
      isLoading: false,
      isLoadingChat: false,

      // 原始对话数据
      allTalksData: [],

      // 用户列表数据
      userList: [],

      // 选中的用户
      selectedUser: {},

      // 当前用户的对话历史
      userChatHistory: [],

      // 当前用户信息
      currentUser: {}
    }
  },
  computed: {
    ...mapState('user', ['userInfo']),

    // 是否可以查看历史记录
    canViewHistory() {
      return this.currentUser && (this.currentUser.level === 1 || this.currentUser.level === 2);
    },

    // 过滤后的用户列表
    filteredUsers() {
      if (!this.searchKeyword) {
        return this.userList;
      }

      return this.userList.filter(user => {
        return user.name && user.name.toLowerCase().includes(this.searchKeyword.toLowerCase());
      });
    }
  },
  onLoad() {
    // 获取当前用户信息
    this.currentUser = getUserInfo();

    // 检查是否已登录
    if (!this.currentUser || !this.currentUser.id) {
      uni.redirectTo({
        url: '/pages/auth/login'
      });
      return;
    }

    // 检查权限
    if (!this.canViewHistory) {
      uni.showToast({
        title: '您没有权限访问此页面',
        icon: 'none'
      });

      // 跳转到AI对话页面
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/chat/ai-chat'
        });
      }, 1500);
      return;
    }

    // 加载对话数据
    this.loadTalksData();
  },
  onShow() {
    // 每次页面显示时刷新数据
    if (this.canViewHistory) {
      this.loadTalksData();
    }
  },
  methods: {
    // 加载对话数据
    async loadTalksData() {
      this.isLoading = true;

      try {
        console.log('开始加载对话数据，当前用户ID:', this.currentUser.id);

        // 调用GetTalks接口
        const response = await userApi.getTalks(this.currentUser.id);

        console.log('GetTalks响应:', response);

        if (response.code === 200 && response.data) {
          this.allTalksData = response.data;
          this.processUserList();
        } else if (response.code === 203) {
          // 超级管理员没有权限查看
          uni.showToast({
            title: '超级管理员无法查看对话记录',
            icon: 'none'
          });
        } else if (response.code === 204) {
          // 没有数据
          this.allTalksData = [];
          this.userList = [];
        } else {
          throw new Error('获取对话数据失败');
        }
      } catch (error) {
        console.error('加载对话数据失败:', error);

        uni.showToast({
          title: error.message || '加载失败',
          icon: 'none'
        });

        this.allTalksData = [];
        this.userList = [];
      } finally {
        this.isLoading = false;
      }
    },

    // 处理用户列表
    processUserList() {
      const userMap = new Map();

      // 遍历所有对话数据，按用户分组
      this.allTalksData.forEach(talk => {
        const userId = talk.id;

        if (!userMap.has(userId)) {
          userMap.set(userId, {
            id: userId,
            name: talk.name,
            level: talk.level,
            picture: talk.picture,
            conversations: [],
            conversationCount: 0,
            lastDate: null,
            lastQuestion: null
          });
        }

        const user = userMap.get(userId);
        user.conversations.push(talk);
        user.conversationCount++;

        // 更新最新的对话时间和问题
        if (!user.lastDate || new Date(talk.date) > new Date(user.lastDate)) {
          user.lastDate = talk.date;
          user.lastQuestion = talk.question;
        }
      });

      // 为每个用户添加模拟的在线状态（基于最后活跃时间）
      userMap.forEach(user => {
        if (user.lastDate) {
          const lastActiveTime = new Date(user.lastDate);
          const now = new Date();
          const timeDiff = now - lastActiveTime;
          // 如果最后活跃时间在30分钟内，显示为在线
          user.isOnline = timeDiff < 30 * 60 * 1000;
        } else {
          user.isOnline = false;
        }
      });

      // 转换为数组并按最新对话时间排序
      this.userList = Array.from(userMap.values()).sort((a, b) => {
        if (!a.lastDate) return 1;
        if (!b.lastDate) return -1;
        return new Date(b.lastDate) - new Date(a.lastDate);
      });

      console.log('处理后的用户列表:', this.userList);
    },

    // 查看用户对话
    viewUserConversations(user) {
      console.log('查看用户对话:', user);

      this.selectedUser = user;
      this.currentView = 'chatDetail';

      // 获取该用户的对话历史
      this.userChatHistory = user.conversations.sort((a, b) => {
        return new Date(b.date) - new Date(a.date);
      });

      console.log('用户对话历史:', this.userChatHistory);
    },

    // 返回用户列表
    backToUserList() {
      this.currentView = 'userList';
      this.selectedUser = {};
      this.userChatHistory = [];
    },

    // 搜索处理
    handleSearch() {
      // 搜索功能已通过计算属性实现
    },

    // 获取级别样式类
    getLevelClass(level) {
      switch (level) {
        case 1:
          return 'level-admin';
        case 2:
          return 'level-manager';
        case 3:
        default:
          return 'level-user';
      }
    },

    // 获取级别名称
    getLevelName(level) {
      switch (level) {
        case 1:
          return '超级管理员';
        case 2:
          return '客户经理';
        case 3:
        default:
          return '普通用户';
      }
    },

    // 获取级别图标
    getLevelIcon(level) {
      switch (level) {
        case 1:
          return '👑';
        case 2:
          return '💼';
        case 3:
        default:
          return '👤';
      }
    },

    // 截断消息文本
    truncateMessage(message) {
      if (!message) return '';
      return message.length > 30 ? message.substring(0, 30) + '...' : message;
    },

    formatDate(dateString) {
      const date = new Date(dateString);
      const now = new Date();

      // 如果是今天的消息，只显示时间
      if (date.toDateString() === now.toDateString()) {
        return this.formatTime(date);
      }

      // 如果是昨天的消息，显示"昨天"
      const yesterday = new Date(now);
      yesterday.setDate(now.getDate() - 1);
      if (date.toDateString() === yesterday.toDateString()) {
        return '昨天 ' + this.formatTime(date);
      }

      // 如果是今年的消息，显示月日
      if (date.getFullYear() === now.getFullYear()) {
        return `${date.getMonth() + 1}月${date.getDate()}日`;
      }

      // 其他情况显示完整日期
      return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;
    },

    formatTime(date) {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    }
  }
}
</script>

<style>
.history-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
}

/* 用户列表视图 */
.user-list-view {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.search-bar {
  padding: 20rpx;
  background-color: #fff;
  position: relative;
}

.search-input {
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  padding: 0 80rpx 0 30rpx;
  font-size: 28rpx;
  width: 100%;
  box-sizing: border-box;
}

.search-icon {
  position: absolute;
  right: 40rpx;
  top: 40rpx;
  font-size: 32rpx;
  color: #999;
}

.user-list {
  flex: 1;
  overflow-y: auto;
  padding: 16rpx 0;
  background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
}

.user-item {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  margin: 0 16rpx 16rpx 16rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.user-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #007AFF, #5AC8FA, #34C759);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.user-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
}

.user-item:active::before {
  opacity: 1;
}

/* 头像容器 */
.user-avatar-container {
  position: relative;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.user-avatar {
  width: 88rpx;
  height: 88rpx;
  border-radius: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.user-avatar.level-admin {
  background: linear-gradient(135deg, #FF9500, #FF6B35);
}

.user-avatar.level-manager {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
}

.user-avatar.level-user {
  background: linear-gradient(135deg, #34C759, #30D158);
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-text {
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 在线状态指示器 */
.online-indicator {
  position: absolute;
  bottom: 4rpx;
  right: 4rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #8E8E93;
  border: 3rpx solid #fff;
  transition: all 0.3s ease;
}

.online-indicator.active {
  background-color: #34C759;
  box-shadow: 0 0 0 4rpx rgba(52, 199, 89, 0.3);
}

/* 用户内容区域 */
.user-content {
  flex: 1;
  overflow: hidden;
  min-width: 0;
}

.user-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.user-name-section {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1d1d1f;
  margin-right: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200rpx;
}

.user-stats {
  display: flex;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60rpx;
}

.stat-number {
  font-size: 24rpx;
  font-weight: bold;
  color: #007AFF;
  line-height: 1;
}

.stat-label {
  font-size: 20rpx;
  color: #8E8E93;
  margin-top: 2rpx;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.last-message-container {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.message-label {
  font-size: 22rpx;
  color: #8E8E93;
  flex-shrink: 0;
}

.user-last-message {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.time-container {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.time-icon {
  font-size: 20rpx;
}

.user-last-time {
  font-size: 24rpx;
  color: #8E8E93;
}

/* 右侧操作区域 */
.user-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  position: relative;
}

.action-button {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
}

.action-icon {
  font-size: 28rpx;
  color: #fff;
  font-weight: bold;
}

.conversation-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #FF3B30, #FF6B35);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 59, 48, 0.4);
}

.badge-text {
  font-size: 20rpx;
  color: #fff;
  font-weight: bold;
  line-height: 1;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  margin: 20rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.8;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.empty-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 15rpx;
}

.empty-subtext {
  font-size: 28rpx;
  color: #8E8E93;
  text-align: center;
  line-height: 1.5;
}

/* 用户级别徽章 */
.user-level-badge {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  font-weight: 600;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.user-level-badge.level-admin {
  background: linear-gradient(135deg, #FF9500, #FF6B35);
  color: #fff;
}

.user-level-badge.level-manager {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  color: #fff;
}

.user-level-badge.level-user {
  background: linear-gradient(135deg, #34C759, #30D158);
  color: #fff;
}

.level-icon {
  font-size: 18rpx;
  line-height: 1;
}

.level-text {
  font-size: 20rpx;
  line-height: 1;
}

/* 对话详情视图 */
.chat-detail-view {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
}

.chat-header {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e5e5e5;
}

.back-button {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.back-icon {
  font-size: 36rpx;
  color: #007AFF;
}

.chat-user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.chat-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #007AFF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15rpx;
  overflow: hidden;
}

.chat-user-details {
  flex: 1;
}

.chat-user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}

.chat-user-level {
  font-size: 24rpx;
  color: #666;
}

/* 对话消息 */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20rpx;
}

.message-item {
  margin-bottom: 40rpx;
}

.message-time {
  text-align: center;
  margin-bottom: 20rpx;
}

.time-text {
  font-size: 24rpx;
  color: #999;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.message-bubble {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.user-message {
  margin-left: 60rpx;
  background-color: #E3F2FD;
}

.ai-message {
  margin-right: 60rpx;
  background-color: #F5F5F5;
}

.message-header {
  margin-bottom: 15rpx;
}

.message-label {
  font-size: 24rpx;
  color: #666;
  font-weight: bold;
}

.message-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  word-break: break-word;
}

/* 空状态 */
.empty-chat-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-chat-state .empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-chat-state .empty-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.empty-chat-state .empty-subtext {
  font-size: 26rpx;
  color: #666;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}
</style>
