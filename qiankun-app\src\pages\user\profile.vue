<template>
  <view class="profile-container">
    <view class="user-header">
      <!-- 用户头像 -->
      <view class="user-avatar" :class="{ 'admin': userInfo.role === 'admin', 'manager': userInfo.role === 'manager' }" @click="changeAvatar">
        <image v-if="userInfo.picture" :src="userInfo.picture" class="avatar-image" mode="aspectFill"></image>
        <text v-else class="avatar-text">{{ userInfo.name ? userInfo.name.substring(0, 1) : '用' }}</text>
        <view class="avatar-edit-hint">
          <text class="edit-icon">📷</text>
        </view>
      </view>
      <view class="user-info">
        <text class="user-name">{{ userInfo.name || '未登录' }}</text>
        <view class="user-status">
          <text class="status-text" :class="{ 'admin': userInfo.role === 'admin', 'manager': userInfo.role === 'manager', 'user': userInfo.role === 'user' }">
            {{ getRoleName(userInfo.role) }} (级别{{ userInfo.level || 3 }})
          </text>
        </view>
        <!-- 显示用户通知 -->
        <view v-if="userInfo.notice" class="user-notice">
          <text class="notice-text">{{ userInfo.notice }}</text>
        </view>
      </view>
    </view>

    <view class="menu-section">
      <view class="menu-title">
        <text>账号信息</text>
      </view>

      <view class="menu-list">
        <view class="menu-item">
          <text class="menu-label">用户名</text>
          <view class="menu-value">
            <text>{{ userInfo.username || '未登录' }}</text>
          </view>
        </view>

        <view class="menu-item">
          <text class="menu-label">用户ID</text>
          <view class="menu-value">
            <text>{{ userInfo.id || '未知' }}</text>
          </view>
        </view>

        <view class="menu-item">
          <text class="menu-label">用户级别</text>
          <view class="menu-value">
            <text>{{ userInfo.level || 3 }}</text>
          </view>
        </view>

        <view class="menu-item" @click="changePassword">
          <text class="menu-label">修改密码</text>
          <view class="menu-value">
            <text class="arrow">></text>
          </view>
        </view>
      </view>
    </view>

    <view class="menu-section">
      <view class="menu-title">
        <text>设置</text>
      </view>

      <view class="menu-list">
        <view class="menu-item" @click="toggleNotification">
          <text class="menu-label">消息通知</text>
          <switch :checked="notificationEnabled" @change="toggleNotification" color="#007AFF" />
        </view>

        <view class="menu-item" @click="clearCache">
          <text class="menu-label">清除缓存</text>
          <view class="menu-value">
            <text class="arrow">></text>
          </view>
        </view>

        <view class="menu-item" @click="showAbout">
          <text class="menu-label">关于我们</text>
          <view class="menu-value">
            <text class="arrow">></text>
          </view>
        </view>
      </view>
    </view>

    <LogoutButton
      v-if="userInfo.id"
      @logout-success="onLogoutSuccess"
      @logout-error="onLogoutError"
    />
    <button class="login-btn" @click="navigateToLogin" v-else>登录/注册</button>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import { chooseImageWithPermission, compressImage } from '@/utils/permission.js';
import LogoutButton from '@/components/LogoutButton.vue';

export default {
  components: {
    LogoutButton
  },
  data() {
    return {
      notificationEnabled: true
    }
  },
  computed: {
    ...mapState('user', ['userInfo'])
  },
  onLoad() {
    // 检查是否已登录
    const token = uni.getStorageSync('token');
    if (!token) {
      uni.redirectTo({
        url: '/pages/auth/login'
      });
    }
  },
  methods: {
    getRoleName(role) {
      switch (role) {
        case 'admin':
          return '超级管理员';
        case 'manager':
          return '客户经理';
        case 'user':
          return '普通用户';
        default:
          return '未知角色';
      }
    },

    // 修改头像
    async changeAvatar() {
      try {
        uni.showActionSheet({
          itemList: ['从相册选择', '拍照'],
          success: async (res) => {
            let sourceType = [];
            if (res.tapIndex === 0) {
              sourceType = ['album'];
            } else if (res.tapIndex === 1) {
              sourceType = ['camera'];
            }

            await this.selectAndUploadAvatar(sourceType);
          }
        });
      } catch (error) {
        console.error('修改头像失败:', error);
        uni.showToast({
          title: '修改头像失败',
          icon: 'none'
        });
      }
    },

    // 选择并上传头像
    async selectAndUploadAvatar(sourceType) {
      try {
        console.log('开始选择头像，来源:', sourceType);

        // 使用权限管理工具选择图片
        const res = await chooseImageWithPermission({
          count: 1,
          sizeType: ['compressed'],
          sourceType: sourceType
        });

        console.log('选择图片成功:', res);
        const tempFilePath = res.tempFilePaths[0];

        // 压缩图片
        const compressedPath = await compressImage(tempFilePath, 0.7);
        console.log('图片压缩完成:', compressedPath);

        // 上传头像
        await this.uploadUserAvatar(compressedPath);

      } catch (error) {
        console.error('选择头像失败:', error);

        let errorMessage = '选择头像失败';
        if (error.message) {
          if (error.message.includes('权限')) {
            errorMessage = '权限不足，请检查相机和相册权限';
          } else if (error.message.includes('取消')) {
            return; // 用户取消，不显示错误
          } else {
            errorMessage = error.message;
          }
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none'
        });
      }
    },

    // 上传用户头像
    async uploadUserAvatar(filePath) {
      uni.showLoading({
        title: '上传中...'
      });

      try {
        console.log('开始上传用户头像:', filePath);

        // 获取token
        const token = uni.getStorageSync('token');
        if (!token) {
          throw new Error('未登录，请先登录');
        }

        // 检查是否为模拟token，如果是则模拟上传成功
        if (token.startsWith('mock-token-')) {
          console.log('模拟头像上传');

          // 模拟上传延迟
          await new Promise(resolve => setTimeout(resolve, 1500));

          // 模拟返回的图片URL
          const mockImageUrl = `https://via.placeholder.com/200x200/667eea/ffffff?text=${encodeURIComponent('头像')}`;

          // 更新本地用户信息
          const userInfo = uni.getStorageSync('userInfo');
          if (userInfo) {
            userInfo.picture = mockImageUrl;
            uni.setStorageSync('userInfo', userInfo);

            // 触发页面更新
            this.$forceUpdate();
          }

          uni.hideLoading();
          uni.showToast({
            title: '头像更新成功',
            icon: 'success'
          });

          return;
        }

        // 真实环境的上传逻辑
        const uploadTask = uni.uploadFile({
          url: 'http://114.55.38.233:11080/upload/avatar',
          filePath: filePath,
          name: 'avatar',
          header: {
            'Authorization': `Bearer ${token}`
          },
          formData: {
            'user_type': 'avatar',
            'user_id': this.userInfo.id
          },
          success: (uploadRes) => {
            console.log('上传成功:', uploadRes);

            try {
              const data = JSON.parse(uploadRes.data);
              if (data.code === 200 && data.data && data.data.url) {
                // 更新本地用户信息
                const userInfo = uni.getStorageSync('userInfo');
                if (userInfo) {
                  userInfo.picture = data.data.url;
                  uni.setStorageSync('userInfo', userInfo);

                  // 触发页面更新
                  this.$forceUpdate();
                }

                uni.showToast({
                  title: '头像更新成功',
                  icon: 'success'
                });
              } else {
                throw new Error(data.message || '上传失败');
              }
            } catch (parseError) {
              console.error('解析上传响应失败:', parseError);
              throw new Error('上传响应格式错误');
            }
          },
          fail: (err) => {
            console.error('上传失败:', err);
            throw new Error('上传失败: ' + (err.errMsg || '网络错误'));
          }
        });

      } catch (error) {
        console.error('头像上传错误:', error);

        uni.showToast({
          title: error.message || '头像上传失败',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },

    changePassword() {
      uni.navigateTo({
        url: '/pages/user/change-password'
      });
    },

    toggleNotification(e) {
      if (typeof e === 'object') {
        this.notificationEnabled = e.detail.value;
      } else {
        this.notificationEnabled = !this.notificationEnabled;
      }

      uni.showToast({
        title: this.notificationEnabled ? '已开启消息通知' : '已关闭消息通知',
        icon: 'none'
      });
    },

    clearCache() {
      uni.showModal({
        title: '清除缓存',
        content: '确定要清除本地缓存吗？这将清除所有对话记录。',
        success: (res) => {
          if (res.confirm) {
            // 清除除了登录信息外的缓存
            const token = uni.getStorageSync('token');
            const userInfo = uni.getStorageSync('userInfo');

            // 清除对话记录
            uni.removeStorageSync('conversations');

            uni.showToast({
              title: '缓存已清除',
              icon: 'success'
            });
          }
        }
      });
    },

    showAbout() {
      uni.showModal({
        title: '关于乾坤袋',
        content: '乾坤袋是一个AI智能对话平台，版本号：1.0.0',
        showCancel: false
      });
    },

    // 退出登录成功事件
    onLogoutSuccess(result) {
      console.log('退出登录成功:', result);
      // 可以在这里添加额外的成功处理逻辑
    },

    // 退出登录失败事件
    onLogoutError(error) {
      console.error('退出登录失败:', error);
      // 可以在这里添加额外的错误处理逻辑
    },

    navigateToLogin() {
      uni.navigateTo({
        url: '/pages/auth/login'
      });
    }
  }
}
</script>

<style>
.profile-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 40rpx;
}

.user-header {
  display: flex;
  align-items: center;
  padding: 40rpx 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: #4CD964;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-avatar:active {
  transform: scale(0.95);
}

.avatar-edit-hint {
  position: absolute;
  bottom: -5rpx;
  right: -5rpx;
  width: 40rpx;
  height: 40rpx;
  background: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  border: 2rpx solid #f5f5f5;
}

.edit-icon {
  font-size: 20rpx;
}

.user-avatar.admin {
  background-color: #FF9500;
}

.user-avatar.manager {
  background-color: #007AFF;
}

.avatar-text {
  color: #fff;
  font-size: 48rpx;
  font-weight: bold;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.user-status {
  display: flex;
  align-items: center;
}

.status-text {
  font-size: 24rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.status-text.admin {
  color: #FF9500;
  background-color: rgba(255, 149, 0, 0.1);
}

.status-text.manager {
  color: #007AFF;
  background-color: rgba(0, 122, 255, 0.1);
}

.status-text.user {
  color: #4CD964;
  background-color: rgba(76, 217, 100, 0.1);
}

.user-notice {
  margin-top: 10rpx;
  padding: 10rpx 15rpx;
  background-color: rgba(255, 149, 0, 0.1);
  border-radius: 8rpx;
  border-left: 4rpx solid #FF9500;
}

.notice-text {
  font-size: 24rpx;
  color: #FF9500;
  line-height: 1.4;
}

.menu-section {
  margin-bottom: 20rpx;
}

.menu-title {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #666;
}

.menu-list {
  background-color: #fff;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f5f5f5;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-label {
  font-size: 30rpx;
  color: #333;
}

.menu-value {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #999;
}

.arrow {
  margin-left: 10rpx;
}

.logout-btn, .login-btn {
  width: 90%;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx;
  font-size: 32rpx;
  margin: 60rpx auto;
}

.logout-btn {
  background-color: #fff;
  color: #FF3B30;
  border: 1px solid #FF3B30;
}

.login-btn {
  background-color: #007AFF;
  color: #fff;
}
</style>
