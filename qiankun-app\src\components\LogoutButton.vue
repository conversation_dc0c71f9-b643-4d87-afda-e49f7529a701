<template>
  <button 
    :class="buttonClass" 
    @click="handleLogout" 
    :disabled="isLoading"
  >
    <view class="button-content">
      <text v-if="!isLoading" class="button-text">{{ buttonText }}</text>
      <view v-else class="loading-content">
        <view class="loading-spinner"></view>
        <text class="button-text">退出中...</text>
      </view>
    </view>
  </button>
</template>

<script>
import { safeLogout } from '@/utils/auth.js'

export default {
  name: 'LogoutButton',
  props: {
    // 按钮文字
    buttonText: {
      type: String,
      default: '退出登录'
    },
    // 按钮样式类
    buttonClass: {
      type: String,
      default: 'logout-btn'
    },
    // 是否显示确认弹窗
    showConfirm: {
      type: Boolean,
      default: true
    },
    // 确认弹窗标题
    confirmTitle: {
      type: String,
      default: '退出登录'
    },
    // 确认弹窗内容
    confirmContent: {
      type: String,
      default: '确定要退出登录吗？'
    },
    // 退出成功后的跳转页面
    redirectUrl: {
      type: String,
      default: '/pages/auth/login'
    }
  },
  data() {
    return {
      isLoading: false
    }
  },
  methods: {
    handleLogout() {
      if (this.showConfirm) {
        uni.showModal({
          title: this.confirmTitle,
          content: this.confirmContent,
          success: (res) => {
            if (res.confirm) {
              this.performLogout();
            }
          }
        });
      } else {
        this.performLogout();
      }
    },
    
    async performLogout() {
      this.isLoading = true;
      
      // 显示加载提示
      uni.showLoading({
        title: '退出中...'
      });
      
      try {
        console.log('开始执行退出登录');
        
        // 调用安全退出登录
        const result = await safeLogout();
        
        uni.hideLoading();
        this.isLoading = false;
        
        if (result.success) {
          // 触发退出成功事件
          this.$emit('logout-success', result);
          
          uni.showToast({
            title: result.message || '已退出登录',
            icon: 'success',
            success: () => {
              // 延迟跳转
              setTimeout(() => {
                this.redirectToLogin();
              }, 1500);
            }
          });
        } else {
          throw new Error(result.message || '退出登录失败');
        }
      } catch (error) {
        console.error('退出登录错误:', error);
        uni.hideLoading();
        this.isLoading = false;
        
        // 触发退出失败事件
        this.$emit('logout-error', error);
        
        // 即使出错也要跳转到登录页
        uni.showToast({
          title: '已退出登录',
          icon: 'success',
          success: () => {
            setTimeout(() => {
              this.redirectToLogin();
            }, 1500);
          }
        });
      }
    },
    
    redirectToLogin() {
      // 触发跳转前事件
      this.$emit('before-redirect');
      
      uni.reLaunch({
        url: this.redirectUrl,
        success: () => {
          // 触发跳转成功事件
          this.$emit('redirect-success');
        },
        fail: (error) => {
          console.error('跳转失败:', error);
          // 触发跳转失败事件
          this.$emit('redirect-error', error);
        }
      });
    }
  }
}
</script>

<style>
.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.button-text {
  font-size: 32rpx;
  font-weight: bold;
}

.loading-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 59, 48, 0.3);
  border-top: 3rpx solid #FF3B30;
  border-radius: 50%;
  margin-right: 15rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 默认退出按钮样式 */
.logout-btn {
  width: 90%;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx;
  font-size: 32rpx;
  margin: 60rpx auto;
  background-color: #fff;
  color: #FF3B30;
  border: 1px solid #FF3B30;
  transition: all 0.3s ease;
}

.logout-btn:active {
  background-color: #FF3B30;
  color: #fff;
}

.logout-btn:disabled {
  opacity: 0.6;
  background-color: #f5f5f5;
  color: #ccc;
  border-color: #ccc;
}
</style>
