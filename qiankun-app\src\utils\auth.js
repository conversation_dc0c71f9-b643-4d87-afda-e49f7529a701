/**
 * 认证相关工具类
 */

// 保存用户登录信息
export const saveUserInfo = (token, userInfo) => {
  uni.setStorageSync('token', token);
  uni.setStorageSync('userInfo', userInfo);
};

// 保存业务人员信息
export const saveBusinessPersonnel = (businessPersonnel) => {
  uni.setStorageSync('businessPersonnel', businessPersonnel);
};

// 获取用户信息
export const getUserInfo = () => {
  return uni.getStorageSync('userInfo') || null;
};

// 获取用户详细信息的各个字段
export const getUserDetail = () => {
  const userInfo = getUserInfo();
  if (!userInfo) return null;

  return {
    id: userInfo.id,
    username: userInfo.username,
    name: userInfo.name,
    level: userInfo.level,
    picture: userInfo.picture,
    notice: userInfo.notice,
    role: userInfo.role
  };
};

// 获取用户头像
export const getUserPicture = () => {
  const userInfo = getUserInfo();
  return userInfo?.picture || '';
};

// 获取用户通知
export const getUserNotice = () => {
  const userInfo = getUserInfo();
  return userInfo?.notice || '';
};

// 获取用户级别
export const getUserLevel = () => {
  const userInfo = getUserInfo();
  return userInfo?.level || 3;
};

// 获取用户角色名称
export const getUserRoleName = () => {
  const userInfo = getUserInfo();
  if (!userInfo) return '未知';

  switch (userInfo.role) {
    case 'admin':
      return '超级管理员';
    case 'manager':
      return '客户经理';
    case 'user':
      return '普通用户';
    default:
      return '未知';
  }
};

// 获取业务人员信息
export const getBusinessPersonnel = () => {
  return uni.getStorageSync('businessPersonnel') || null;
};

// 获取 token
export const getToken = () => {
  return uni.getStorageSync('token') || '';
};

// 检查是否已登录
export const isLoggedIn = () => {
  return !!getToken();
};

// 检查是否已绑定业务人员
export const hasBusinessPersonnel = () => {
  return !!getBusinessPersonnel();
};

// 清除登录信息
export const clearLoginInfo = () => {
  uni.removeStorageSync('token');
  uni.removeStorageSync('userInfo');
};

// 清除所有认证信息
export const clearAllAuthInfo = () => {
  clearLoginInfo();
  uni.removeStorageSync('businessPersonnel');
};

// 检查是否已实名认证
export const isRealNameVerified = () => {
  const userInfo = getUserInfo();
  return userInfo && userInfo.isRealNameVerified;
};
