<template>
  <view class="add-user-page">
    <!-- 顶部装饰 -->
    <view class="header-decoration">
      <view class="decoration-circle circle-1"></view>
      <view class="decoration-circle circle-2"></view>
    </view>
    
    <!-- 主要内容 -->
    <view class="content-container">
      <!-- 标题区域 -->
      <view class="header-section">
        <view class="icon-container">
          <text class="user-icon">👤</text>
        </view>
        <text class="main-title">创建新用户</text>
        <text class="sub-title">为团队添加新成员</text>
      </view>
      
      <!-- 表单区域 -->
      <view class="form-section">
        <view class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <text class="icon">📝</text>
            </view>
            <input 
              class="form-input" 
              type="text" 
              placeholder="请输入用户名" 
              v-model="form.username"
            />
          </view>
        </view>
        
        <view class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <text class="icon">🔒</text>
            </view>
            <input 
              class="form-input" 
              type="password" 
              placeholder="请输入密码" 
              v-model="form.password"
              password
            />
          </view>
        </view>
        
        <view class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <text class="icon">📱</text>
            </view>
            <input 
              class="form-input" 
              type="text" 
              placeholder="请输入手机号（可选）" 
              v-model="form.phone"
            />
          </view>
        </view>
        
        <!-- 头像上传 -->
        <view class="avatar-upload-section">
          <text class="section-title">用户头像</text>
          <view class="avatar-upload-container">
            <view class="avatar-preview" @click="chooseAvatar">
              <image
                v-if="form.picture"
                :src="form.picture"
                class="avatar-image"
                mode="aspectFill"
              ></image>
              <view v-else class="avatar-placeholder">
                <text class="upload-icon">📷</text>
                <text class="upload-text">点击上传头像</text>
              </view>
            </view>
            <view class="avatar-actions">
              <button class="upload-btn" @click="chooseAvatar" :disabled="isUploading">
                <text v-if="!isUploading">选择头像</text>
                <text v-else>上传中...</text>
              </button>
              <button v-if="form.picture" class="remove-btn" @click="removeAvatar">
                <text>移除头像</text>
              </button>
            </view>
          </view>
        </view>
        
        <!-- 用户权限选择 -->
        <view class="permission-section">
          <text class="section-title">用户权限</text>
          <view class="permission-options">
            <view 
              class="permission-item" 
              v-for="option in availablePermissions" 
              :key="option.value"
              :class="{ 'selected': form.user_permission === option.value }"
              @click="selectPermission(option.value)"
            >
              <view class="permission-icon">
                <text>{{ option.icon }}</text>
              </view>
              <view class="permission-info">
                <text class="permission-name">{{ option.name }}</text>
                <text class="permission-desc">{{ option.description }}</text>
              </view>
              <view class="permission-check" v-if="form.user_permission === option.value">
                <text>✓</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 通知设置 -->
        <view class="notice-section">
          <text class="section-title">通知设置</text>
          <view class="notice-toggle">
            <text class="notice-label">启用消息通知</text>
            <switch 
              :checked="form.notice === 1" 
              @change="toggleNotice" 
              color="#667eea" 
            />
          </view>
        </view>
        
        <!-- 按钮区域 -->
        <view class="button-section">
          <button class="primary-button" @click="handleCreateUser" :disabled="isLoading">
            <view class="button-content">
              <text v-if="!isLoading" class="button-text">创建用户</text>
              <view v-else class="loading-content">
                <view class="loading-spinner"></view>
                <text class="button-text">创建中...</text>
              </view>
            </view>
          </button>
          
          <button class="secondary-button" @click="goBack" :disabled="isLoading">
            <text class="button-text">取消</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { userApi } from '@/utils/api.js'
import { getUserInfo } from '@/utils/auth.js'
import { chooseImageWithPermission, compressImage } from '@/utils/permission.js'

export default {
  data() {
    return {
      form: {
        username: '',
        password: '',
        phone: '',
        picture: '',
        notice: 1,
        user_permission: 3, // 默认为普通用户
        father_id: null
      },
      isLoading: false,
      isUploading: false,
      currentUser: {}
    }
  },
  computed: {
    // 根据当前用户权限显示可选的权限级别
    availablePermissions() {
      const currentUser = this.currentUser;
      const permissions = [];
      
      if (currentUser.role === 'admin') {
        // 超级管理员可以创建经理和普通用户
        permissions.push(
          {
            value: 2,
            name: '客户经理',
            description: '可以管理普通用户',
            icon: '👔'
          },
          {
            value: 3,
            name: '普通用户',
            description: '基础功能权限',
            icon: '👤'
          }
        );
      } else if (currentUser.role === 'manager') {
        // 客户经理只能创建普通用户
        permissions.push({
          value: 3,
          name: '普通用户',
          description: '基础功能权限',
          icon: '👤'
        });
      }
      
      return permissions;
    }
  },
  onLoad() {
    // 获取当前用户信息
    this.currentUser = getUserInfo() || {};
    
    // 检查权限
    if (!this.currentUser.role || this.currentUser.role === 'user') {
      uni.showToast({
        title: '您没有权限访问此页面',
        icon: 'none'
      });
      
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
      return;
    }
    
    // 设置父级ID
    this.form.father_id = this.currentUser.id;
  },
  methods: {
    // 选择权限
    selectPermission(value) {
      this.form.user_permission = value;
    },
    
    // 切换通知设置
    toggleNotice(e) {
      this.form.notice = e.detail.value ? 1 : 0;
    },
    
    // 验证表单
    validateForm() {
      if (!this.form.username) {
        uni.showToast({
          title: '请输入用户名',
          icon: 'none'
        });
        return false;
      }
      
      if (this.form.username.length < 3) {
        uni.showToast({
          title: '用户名至少3位字符',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.form.password) {
        uni.showToast({
          title: '请输入密码',
          icon: 'none'
        });
        return false;
      }
      
      if (this.form.password.length < 6) {
        uni.showToast({
          title: '密码至少6位字符',
          icon: 'none'
        });
        return false;
      }
      
      if (this.form.phone && !/^1[3-9]\d{9}$/.test(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        });
        return false;
      }
      
      return true;
    },
    
    // 创建用户
    async handleCreateUser() {
      console.log('handleCreateUser 方法被调用');

      if (!this.validateForm()) {
        console.log('表单验证失败');
        return;
      }

      console.log('表单验证通过，开始创建用户');
      this.isLoading = true;

      try {
        console.log('开始创建用户:', this.form);
        console.log('当前token:', uni.getStorageSync('token'));

        // 调用新增用户API
        const response = await userApi.addUser(this.form);

        console.log('创建用户响应:', response);
        
        if (response && response.code === 200) {
          // 创建成功
          uni.showToast({
            title: '用户创建成功',
            icon: 'success',
            duration: 2000,
            success: () => {
              setTimeout(() => {
                uni.navigateBack();
              }, 2000);
            }
          });
        } else {
          // 创建失败
          let errorMessage = '创建用户失败';
          if (response && response.code) {
            switch (response.code) {
              case 400:
                errorMessage = '参数错误';
                break;
              case 401:
                errorMessage = '权限不足';
                break;
              case 409:
                errorMessage = '用户名已存在';
                break;
              case 500:
                errorMessage = '服务器内部错误';
                break;
              default:
                errorMessage = response.message || '创建用户失败';
            }
          }
          
          uni.showToast({
            title: errorMessage,
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('创建用户错误:', error);
        
        let errorMessage = '网络错误，请稍后重试';
        if (error.message) {
          if (error.message.includes('未登录')) {
            errorMessage = '登录已过期，请重新登录';
          } else {
            errorMessage = error.message;
          }
        }
        
        uni.showToast({
          title: errorMessage,
          icon: 'none'
        });
      } finally {
        this.isLoading = false;
      }
    },
    
    // 选择头像
    async chooseAvatar() {
      try {
        uni.showActionSheet({
          itemList: ['从相册选择', '拍照'],
          success: async (res) => {
            let sourceType = [];
            if (res.tapIndex === 0) {
              sourceType = ['album'];
            } else if (res.tapIndex === 1) {
              sourceType = ['camera'];
            }

            await this.selectImage(sourceType);
          }
        });
      } catch (error) {
        console.error('选择头像失败:', error);
        uni.showToast({
          title: '选择头像失败',
          icon: 'none'
        });
      }
    },

    // 选择图片
    async selectImage(sourceType) {
      try {
        console.log('开始选择图片，来源:', sourceType);

        // 使用权限管理工具选择图片
        const res = await chooseImageWithPermission({
          count: 1,
          sizeType: ['compressed'],
          sourceType: sourceType
        });

        console.log('选择图片成功:', res);
        const tempFilePath = res.tempFilePaths[0];

        // 压缩图片
        const compressedPath = await compressImage(tempFilePath, 0.7);
        console.log('图片压缩完成:', compressedPath);

        // 显示预览
        this.form.picture = compressedPath;

        // 上传图片
        await this.uploadAvatar(compressedPath);

      } catch (error) {
        console.error('选择图片失败:', error);

        let errorMessage = '选择图片失败';
        if (error.message) {
          if (error.message.includes('权限')) {
            errorMessage = '权限不足，请检查相机和相册权限';
          } else if (error.message.includes('取消')) {
            return; // 用户取消，不显示错误
          } else {
            errorMessage = error.message;
          }
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none'
        });
      }
    },

    // 上传头像
    async uploadAvatar(filePath) {
      this.isUploading = true;

      try {
        console.log('开始上传头像:', filePath);

        // 获取token
        const token = uni.getStorageSync('token');
        if (!token) {
          throw new Error('未登录，请先登录');
        }

        // 检查是否为模拟token，如果是则模拟上传成功
        if (token.startsWith('mock-token-')) {
          console.log('模拟头像上传');

          // 模拟上传延迟
          await new Promise(resolve => setTimeout(resolve, 1500));

          // 模拟返回的图片URL
          const mockImageUrl = `https://via.placeholder.com/200x200/4facfe/ffffff?text=${encodeURIComponent('头像')}`;
          this.form.picture = mockImageUrl;

          uni.showToast({
            title: '头像上传成功',
            icon: 'success'
          });

          return;
        }

        // 真实环境的上传逻辑
        const uploadTask = uni.uploadFile({
          url: 'http://49cw9260ms55.vicp.fun/system/upload/avatar', // 根据实际情况修改上传接口
          filePath: filePath,
          name: 'avatar',
          header: {
            'Authorization': `Bearer ${token}`
          },
          formData: {
            'user_type': 'avatar'
          },
          success: (uploadRes) => {
            console.log('上传成功:', uploadRes);

            try {
              const data = JSON.parse(uploadRes.data);
              if (data.code === 200 && data.data && data.data.url) {
                this.form.picture = data.data.url;
                uni.showToast({
                  title: '头像上传成功',
                  icon: 'success'
                });
              } else {
                throw new Error(data.message || '上传失败');
              }
            } catch (parseError) {
              console.error('解析上传响应失败:', parseError);
              throw new Error('上传响应格式错误');
            }
          },
          fail: (err) => {
            console.error('上传失败:', err);
            throw new Error('上传失败: ' + (err.errMsg || '网络错误'));
          }
        });

        // 监听上传进度
        uploadTask.onProgressUpdate((res) => {
          console.log('上传进度:', res.progress + '%');
        });

      } catch (error) {
        console.error('头像上传错误:', error);

        // 恢复原来的图片或清空
        this.form.picture = '';

        uni.showToast({
          title: error.message || '头像上传失败',
          icon: 'none'
        });
      } finally {
        this.isUploading = false;
      }
    },

    // 移除头像
    removeAvatar() {
      uni.showModal({
        title: '确认移除',
        content: '确定要移除当前头像吗？',
        success: (res) => {
          if (res.confirm) {
            this.form.picture = '';
            uni.showToast({
              title: '头像已移除',
              icon: 'success'
            });
          }
        }
      });
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    }
  }
}
</script>

<style>
/* 复用修改密码页面的样式，并添加特定样式 */
.add-user-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  position: relative;
  overflow: hidden;
}

/* 顶部装饰 */
.header-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 300rpx;
  overflow: hidden;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: -100rpx;
  right: -50rpx;
  animation: float 6s ease-in-out infinite;
}

.circle-2 {
  width: 150rpx;
  height: 150rpx;
  top: 50rpx;
  left: -75rpx;
  animation: float 8s ease-in-out infinite reverse;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* 主要内容 */
.content-container {
  position: relative;
  z-index: 10;
  padding: 100rpx 40rpx 40rpx;
}

/* 标题区域 */
.header-section {
  text-align: center;
  margin-bottom: 60rpx;
}

.icon-container {
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 40rpx;
  backdrop-filter: blur(10px);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.user-icon {
  font-size: 60rpx;
}

.main-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  display: block;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.3);
}

.sub-title {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

/* 表单区域 */
.form-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 60rpx 40rpx;
  backdrop-filter: blur(20px);
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

/* 输入框组 */
.input-group {
  margin-bottom: 30rpx;
}

.input-wrapper {
  position: relative;
  background: #f8f9fa;
  border-radius: 20rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  overflow: hidden;
}

.input-wrapper:focus-within {
  border-color: #4facfe;
  background: #fff;
  box-shadow: 0 0 0 6rpx rgba(79, 172, 254, 0.1);
}

.input-icon {
  position: absolute;
  left: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.icon {
  font-size: 32rpx;
}

.form-input {
  width: 100%;
  height: 100rpx;
  padding: 0 30rpx 0 80rpx;
  font-size: 32rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}

.form-input::placeholder {
  color: #999;
}

/* 头像上传区域 */
.avatar-upload-section {
  margin: 40rpx 0;
}

.avatar-upload-container {
  display: flex;
  align-items: center;
  gap: 30rpx;
}

.avatar-preview {
  width: 150rpx;
  height: 150rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 3rpx solid #e5e5e5;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.avatar-preview:active {
  transform: scale(0.95);
  border-color: #4facfe;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.upload-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

.upload-text {
  font-size: 20rpx;
  text-align: center;
  line-height: 1.2;
}

.avatar-actions {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.upload-btn, .remove-btn {
  height: 60rpx;
  border-radius: 30rpx;
  border: none;
  font-size: 26rpx;
  transition: all 0.3s ease;
}

.upload-btn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: #fff;
}

.upload-btn:disabled {
  opacity: 0.6;
  background: #ccc;
}

.upload-btn:active {
  transform: scale(0.95);
}

.remove-btn {
  background: #fff;
  color: #ff4757;
  border: 2rpx solid #ff4757;
}

.remove-btn:active {
  background: #ff4757;
  color: #fff;
}

/* 权限选择区域 */
.permission-section, .notice-section {
  margin: 40rpx 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 30rpx;
}

.permission-options {
  background: #f8f9fa;
  border-radius: 20rpx;
  overflow: hidden;
}

.permission-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  transition: all 0.3s ease;
  position: relative;
}

.permission-item:last-child {
  border-bottom: none;
}

.permission-item.selected {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: #fff;
}

.permission-item.selected .permission-name,
.permission-item.selected .permission-desc {
  color: #fff;
}

.permission-icon {
  width: 80rpx;
  height: 80rpx;
  background: rgba(79, 172, 254, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
  font-size: 40rpx;
}

.permission-item.selected .permission-icon {
  background: rgba(255, 255, 255, 0.2);
}

.permission-info {
  flex: 1;
}

.permission-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.permission-desc {
  font-size: 26rpx;
  color: #666;
}

.permission-check {
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
}

/* 通知设置 */
.notice-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
}

.notice-label {
  font-size: 30rpx;
  color: #333;
}

/* 按钮区域 */
.button-section {
  margin-top: 60rpx;
}

.primary-button {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 50rpx;
  border: none;
  margin-bottom: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(79, 172, 254, 0.3);
  transition: all 0.3s ease;
}

.primary-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 5rpx 15rpx rgba(79, 172, 254, 0.4);
}

.primary-button:disabled {
  opacity: 0.7;
  transform: none;
}

.secondary-button {
  width: 100%;
  height: 100rpx;
  background: transparent;
  border: 2rpx solid #ddd;
  border-radius: 50rpx;
  transition: all 0.3s ease;
}

.secondary-button:active {
  background: #f5f5f5;
  border-color: #ccc;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.button-text {
  font-size: 32rpx;
  font-weight: bold;
}

.primary-button .button-text {
  color: #fff;
}

.secondary-button .button-text {
  color: #666;
}

.loading-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid #fff;
  border-radius: 50%;
  margin-right: 15rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
