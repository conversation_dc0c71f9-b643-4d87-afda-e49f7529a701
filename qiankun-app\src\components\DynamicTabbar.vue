<template>
  <view class="dynamic-tab-bar">
    <view 
      class="tab-item" 
      v-for="(tab, index) in visibleTabs" 
      :key="index"
      :class="{ 'active': currentTab === tab.path }"
      @click="switchTab(tab.path)"
    >
      <view class="tab-icon">
        <uni-icons 
          :type="tab.icon" 
          :size="24" 
          :color="currentTab === tab.path ? '#007AFF' : '#7A7E83'"
        ></uni-icons>
      </view>
      <text class="tab-text" :class="{ 'active': currentTab === tab.path }">
        {{ tab.text }}
      </text>
    </view>
  </view>
</template>

<script>
import { getCurrentTabbar, hasPagePermission } from '@/utils/tabbar.js';

// 避免循环导入，直接从本地存储获取用户信息
const getUserInfo = () => {
  return uni.getStorageSync('userInfo') || null;
};

export default {
  name: 'DynamicTabBar',
  props: {
    current: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      currentTab: '',
      tabbarList: []
    }
  },
  computed: {
    visibleTabs() {
      return this.tabbarList.map(item => ({
        path: `/${item.pagePath}`,
        icon: this.getIconName(item.pagePath),
        text: item.text
      }));
    }
  },
  mounted() {
    this.initTabbar();
    this.currentTab = this.current || this.getCurrentPath();
  },
  watch: {
    current(newVal) {
      this.currentTab = newVal;
    }
  },
  methods: {
    // 初始化tabbar
    initTabbar() {
      this.tabbarList = getCurrentTabbar();
      console.log('DynamicTabbar初始化:', this.tabbarList);
    },
    
    // 获取图标名称
    getIconName(pagePath) {
      const iconMap = {
        'pages/chat/ai-chat': 'chat',
        'pages/chat/chat-history': 'list',
        'pages/admin/user-management': 'contact',
        'pages/user/profile': 'person'
      };
      return iconMap[pagePath] || 'home';
    },
    
    // 获取当前页面路径
    getCurrentPath() {
      const pages = getCurrentPages();
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        return `/${currentPage.route}`;
      }
      return '';
    },
    
    // 切换tab
    switchTab(path) {
      if (this.currentTab === path) {
        return; // 已经是当前页面，不需要跳转
      }
      
      // 检查权限
      const userInfo = getUserInfo();
      const pagePath = path.substring(1); // 移除开头的 /
      
      if (!hasPagePermission(pagePath, userInfo)) {
        uni.showToast({
          title: '您没有权限访问此页面',
          icon: 'none'
        });
        return;
      }
      
      // 跳转页面
      uni.switchTab({
        url: path,
        success: () => {
          this.currentTab = path;
          this.$emit('change', path);
        },
        fail: (error) => {
          console.error('切换tab失败:', error);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    },
    
    // 刷新tabbar
    refreshTabbar() {
      this.initTabbar();
      this.currentTab = this.getCurrentPath();
    }
  }
};
</script>

<style scoped>
.dynamic-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #ffffff;
  border-top: 1rpx solid #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 1000;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10rpx 0;
  transition: all 0.3s ease;
}

.tab-item.active {
  transform: scale(1.05);
}

.tab-icon {
  margin-bottom: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-text {
  font-size: 20rpx;
  color: #7A7E83;
  transition: color 0.3s ease;
}

.tab-text.active {
  color: #007AFF;
  font-weight: 600;
}

/* 适配不同屏幕 */
@media screen and (max-width: 750rpx) {
  .dynamic-tab-bar {
    height: 90rpx;
  }
  
  .tab-text {
    font-size: 18rpx;
  }
}
</style>
