<script>
import { initUserTabbar, updateUserTabbar } from '@/utils/tabbar.js';

export default {
  onLaunch: function () {
    console.log('App Launch');

    // 初始化tabbar
    this.initAppTabbar();
  },
  onShow: function () {
    console.log('App Show');

    // 每次应用显示时检查tabbar状态
    this.checkTabbarStatus();
  },
  onHide: function () {
    console.log('App Hide');
  },
  methods: {
    initAppTabbar() {
      try {
        initUserTabbar();
        console.log('应用启动时tabbar初始化完成');
      } catch (error) {
        console.error('应用启动时tabbar初始化失败:', error);
      }
    },

    checkTabbarStatus() {
      try {
        updateUserTabbar();
        console.log('应用显示时tabbar状态检查完成');
      } catch (error) {
        console.error('应用显示时tabbar状态检查失败:', error);
      }
    }
  }
}
</script>

<style>
/*每个页面公共css */
</style>
