<template>
  <view class="page-container">
    <view class="card">
      <view class="title">修改密码</view>
      <view class="subtitle">为了您的账户安全，请定期修改密码</view>
      
      <view class="form-container">
        <view class="form-group">
          <view class="form-label">原密码</view>
          <input 
            class="form-input" 
            type="password" 
            placeholder="请输入原密码" 
            v-model="form.oldPassword"
            password
          />
        </view>
        
        <view class="form-group">
          <view class="form-label">新密码</view>
          <input 
            class="form-input" 
            type="password" 
            placeholder="请输入新密码" 
            v-model="form.newPassword"
            password
          />
        </view>
        
        <view class="form-group">
          <view class="form-label">确认新密码</view>
          <input 
            class="form-input" 
            type="password" 
            placeholder="请再次输入新密码" 
            v-model="form.confirmPassword"
            password
          />
        </view>
        
        <view class="password-tips">
          <text class="tips-title">密码要求：</text>
          <text class="tips-item">• 长度至少6位</text>
          <text class="tips-item">• 建议包含字母和数字</text>
          <text class="tips-item">• 避免使用简单密码</text>
        </view>
        
        <button class="btn btn-primary" @click="handleChangePassword" :disabled="isLoading">
          <text v-if="!isLoading">确认修改</text>
          <text v-else>修改中...</text>
        </button>
        
        <button class="btn btn-secondary" @click="goBack" :disabled="isLoading">
          <text>取消</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { userApi } from '@/utils/api.js'
import { getUserInfo } from '@/utils/auth.js'

export default {
  data() {
    return {
      form: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      isLoading: false
    }
  },
  methods: {
    // 验证表单
    validateForm() {
      if (!this.form.oldPassword) {
        uni.showToast({
          title: '请输入原密码',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.form.newPassword) {
        uni.showToast({
          title: '请输入新密码',
          icon: 'none'
        });
        return false;
      }
      
      if (this.form.newPassword.length < 6) {
        uni.showToast({
          title: '新密码长度至少6位',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.form.confirmPassword) {
        uni.showToast({
          title: '请确认新密码',
          icon: 'none'
        });
        return false;
      }
      
      if (this.form.newPassword !== this.form.confirmPassword) {
        uni.showToast({
          title: '两次输入的新密码不一致',
          icon: 'none'
        });
        return false;
      }
      
      if (this.form.oldPassword === this.form.newPassword) {
        uni.showToast({
          title: '新密码不能与原密码相同',
          icon: 'none'
        });
        return false;
      }
      
      return true;
    },
    
    // 处理修改密码
    async handleChangePassword() {
      if (!this.validateForm()) {
        return;
      }

      // 获取用户信息
      const userInfo = getUserInfo();
      if (!userInfo || !userInfo.id) {
        uni.showToast({
          title: '用户信息异常，请重新登录',
          icon: 'none'
        });
        return;
      }

      this.isLoading = true;

      try {
        console.log('开始修改密码，用户ID:', userInfo.id);

        // 先验证原密码是否正确（通过登录接口验证）
        try {
          await userApi.login(userInfo.username, this.form.oldPassword);
          console.log('原密码验证成功');
        } catch (loginError) {
          console.error('原密码验证失败:', loginError);
          uni.showToast({
            title: '原密码错误',
            icon: 'none'
          });
          this.isLoading = false;
          return;
        }

        // 调用修改密码API
        const response = await userApi.changePassword(
          userInfo.id,
          this.form.oldPassword,
          this.form.newPassword
        );

        console.log('修改密码响应:', response);

        if (response && response.code === 200) {
          // 修改成功
          uni.showToast({
            title: '密码修改成功',
            icon: 'success',
            duration: 2000,
            success: () => {
              setTimeout(() => {
                this.goBack();
              }, 2000);
            }
          });
        } else {
          // 修改失败
          let errorMessage = '修改密码失败';
          if (response && response.code) {
            switch (response.code) {
              case 400:
                errorMessage = '参数错误';
                break;
              case 401:
                errorMessage = '原密码错误';
                break;
              case 204:
                errorMessage = '用户不存在';
                break;
              case 500:
                errorMessage = '服务器内部错误';
                break;
              default:
                errorMessage = response.message || '修改密码失败';
            }
          }

          uni.showToast({
            title: errorMessage,
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('修改密码错误:', error);

        let errorMessage = '网络错误，请稍后重试';
        if (error.message) {
          if (error.message.includes('未登录')) {
            errorMessage = '登录已过期，请重新登录';
          } else {
            errorMessage = error.message;
          }
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none'
        });
      } finally {
        this.isLoading = false;
      }
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack();
    }
  }
}
</script>

<style>
.form-container {
  padding: 40rpx 0;
}

.password-tips {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 30rpx;
  margin: 30rpx 0;
  border-left: 4rpx solid #007AFF;
}

.tips-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.tips-item {
  font-size: 26rpx;
  color: #666;
  display: block;
  line-height: 1.6;
  margin-bottom: 8rpx;
}

.btn {
  margin-bottom: 20rpx;
}
</style>
