export default {
  namespaced: true,
  state: {
    token: uni.getStorageSync('token') || '',
    userInfo: uni.getStorageSync('userInfo') || {}
  },
  mutations: {
    SET_TOKEN(state, token) {
      state.token = token;
      uni.setStorageSync('token', token);
    },
    SET_USER_INFO(state, userInfo) {
      state.userInfo = userInfo;
      uni.setStorageSync('userInfo', userInfo);
    },
    CLEAR_USER(state) {
      state.token = '';
      state.userInfo = {};
      uni.removeStorageSync('token');
      uni.removeStorageSync('userInfo');
    }
  },
  actions: {
    // 登录
    login({ commit }, { username, password }) {
      return new Promise((resolve) => {
        // 这里应该是实际的登录API调用
        setTimeout(() => {
          // 模拟登录成功
          const token = 'sample-token';

          // 根据用户名判断角色
          let role = 'user';
          let name = '普通用户';

          if (username === 'admin') {
            role = 'admin';
            name = '超级管理员';
          } else if (username === 'manager') {
            role = 'manager';
            name = '客户经理';
          }

          const userInfo = {
            id: '1',
            username: username,
            name: name,
            role: role
          };

          commit('SET_TOKEN', token);
          commit('SET_USER_INFO', userInfo);

          resolve({
            code: 200,
            message: '登录成功',
            data: userInfo
          });
        }, 1000);
      });
    },

    // 创建用户
    createUser({ commit }, userData) {
      return new Promise((resolve) => {
        // 这里应该是实际的创建用户API调用
        setTimeout(() => {
          // 模拟创建成功
          resolve({
            code: 200,
            message: '创建成功',
            data: {
              id: Date.now().toString(),
              ...userData
            }
          });
        }, 1000);
      });
    },

    // 更新用户
    updateUser({ commit }, userData) {
      return new Promise((resolve) => {
        // 这里应该是实际的更新用户API调用
        setTimeout(() => {
          // 模拟更新成功
          resolve({
            code: 200,
            message: '更新成功',
            data: userData
          });
        }, 1000);
      });
    },

    // 退出登录
    logout({ commit }) {
      return new Promise((resolve) => {
        commit('CLEAR_USER');
        resolve({
          code: 200,
          message: '退出成功'
        });
      });
    }
  },
  getters: {
    isLoggedIn: state => !!state.token,
    isAdmin: state => state.userInfo.role === 'admin',
    isManager: state => state.userInfo.role === 'manager',
    isUser: state => state.userInfo.role === 'user'
  }
};
