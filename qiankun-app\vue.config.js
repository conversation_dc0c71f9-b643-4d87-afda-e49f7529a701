module.exports = {
  devServer: {
    proxy: {
      '/login': {
        target: 'http://114.55.38.233:11080',
        changeOrigin: true,
        ws: true,
        secure: false,
        pathRewrite: {
          '^/login': '/login'
        },
        onProxyReq: function(proxyReq, req, res) {
          // 打印代理请求信息
          console.log('登录代理请求:', req.method, req.url);
        },
        onProxyRes: function(proxyRes, req, res) {
          // 打印代理响应信息
          console.log('登录代理响应:', proxyRes.statusCode, req.url);
        }
      },
      '/GetUser': {
        target: 'http://114.55.38.233:11080',
        changeOrigin: true,
        ws: true,
        secure: false,
        pathRewrite: {
          '^/GetUser': '/GetUser'
        },
        onProxyReq: function(proxyReq, req, res) {
          // 打印代理请求信息
          console.log('获取用户信息代理请求:', req.method, req.url);
        },
        onProxyRes: function(proxyRes, req, res) {
          // 打印代理响应信息
          console.log('获取用户信息代理响应:', proxyRes.statusCode, req.url);
        }
      },
      '/ChangePassword': {
        target: 'http://114.55.38.233:11080',
        changeOrigin: true,
        ws: true,
        secure: false,
        pathRewrite: {
          '^/ChangePassword': '/ChangePassword'
        },
        onProxyReq: function(proxyReq, req, res) {
          // 打印代理请求信息
          console.log('修改密码代理请求:', req.method, req.url);
        },
        onProxyRes: function(proxyRes, req, res) {
          // 打印代理响应信息
          console.log('修改密码代理响应:', proxyRes.statusCode, req.url);
        }
      },
      '/AddUser': {
        target: 'http://114.55.38.233:11080',
        changeOrigin: true,
        ws: true,
        secure: false,
        pathRewrite: {
          '^/AddUser': '/AddUser'
        },
        onProxyReq: function(proxyReq, req, res) {
          // 打印代理请求信息
          console.log('新增用户代理请求:', req.method, req.url);
        },
        onProxyRes: function(proxyRes, req, res) {
          // 打印代理响应信息
          console.log('新增用户代理响应:', proxyRes.statusCode, req.url);
        }
      },
      '/v1': {
        target: 'http://114.55.38.233:11080',
        changeOrigin: true,
        ws: true,
        secure: false,
        pathRewrite: {
          '^/v1': '/v1'
        },
        onProxyReq: function(proxyReq, req, res) {
          // 打印代理请求信息
          console.log('代理请求:', req.method, req.url);
        },
        onProxyRes: function(proxyRes, req, res) {
          // 打印代理响应信息
          console.log('代理响应:', proxyRes.statusCode, req.url);
        }
      }
    }
  }
}
