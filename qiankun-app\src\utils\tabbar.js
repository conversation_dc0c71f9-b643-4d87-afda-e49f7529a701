/**
 * 动态Tabbar管理工具
 * 根据用户权限动态显示/隐藏tabbar项
 */

import { getUserInfo } from './auth.js';

// 默认的tabbar配置
const DEFAULT_TABBAR_LIST = [
  {
    pagePath: "pages/chat/ai-chat",
    iconPath: "static/images/chat.png",
    selectedIconPath: "static/images/chat-active.png",
    text: "AI对话"
  },
  {
    pagePath: "pages/chat/chat-history",
    iconPath: "static/images/history.png",
    selectedIconPath: "static/images/history-active.png",
    text: "历史记录"
  },
  {
    pagePath: "pages/admin/user-management",
    iconPath: "static/images/users.png",
    selectedIconPath: "static/images/users-active.png",
    text: "用户管理"
  },
  {
    pagePath: "pages/user/profile",
    iconPath: "static/images/user.png",
    selectedIconPath: "static/images/user-active.png",
    text: "我的"
  }
];

// 基础tabbar配置（所有用户都能看到的）
const BASE_TABBAR_LIST = [
  {
    pagePath: "pages/chat/ai-chat",
    iconPath: "static/images/chat.png",
    selectedIconPath: "static/images/chat-active.png",
    text: "AI对话"
  },
  {
    pagePath: "pages/user/profile",
    iconPath: "static/images/user.png",
    selectedIconPath: "static/images/user-active.png",
    text: "我的"
  }
];

/**
 * 根据用户权限获取tabbar配置
 * @param {Object} userInfo - 用户信息
 * @returns {Array} tabbar配置数组
 */
export const getTabbarByUserLevel = (userInfo) => {
  if (!userInfo || !userInfo.level) {
    console.log('用户信息不完整，使用基础tabbar');
    return BASE_TABBAR_LIST;
  }

  const tabbarList = [...BASE_TABBAR_LIST];

  // 根据用户级别添加相应的tabbar项
  if (userInfo.level === 1 || userInfo.level === 2) {
    // 超级管理员(1)和客户经理(2)可以看到历史记录和用户管理
    
    // 在AI对话后插入历史记录
    tabbarList.splice(1, 0, {
      pagePath: "pages/chat/chat-history",
      iconPath: "static/images/history.png",
      selectedIconPath: "static/images/history-active.png",
      text: "历史记录"
    });

    // 在我的前插入用户管理
    tabbarList.splice(2, 0, {
      pagePath: "pages/admin/user-management",
      iconPath: "static/images/users.png",
      selectedIconPath: "static/images/users-active.png",
      text: "用户管理"
    });
  }

  console.log('根据用户级别生成tabbar:', userInfo.level, tabbarList);
  return tabbarList;
};

/**
 * 设置动态tabbar
 * @param {Array} tabbarList - tabbar配置数组
 */
export const setDynamicTabbar = (tabbarList) => {
  try {
    // 小程序环境下使用自定义tabbar
    // #ifdef MP-WEIXIN
    if (typeof getApp === 'function') {
      const app = getApp();
      if (app.globalData) {
        app.globalData.tabbarList = tabbarList;
      }
    }
    // #endif

    // H5和APP环境下的处理
    // #ifndef MP-WEIXIN
    console.log('H5/APP环境，tabbar配置已更新');
    // #endif

    console.log('动态tabbar设置完成:', tabbarList);
  } catch (error) {
    console.error('设置动态tabbar失败:', error);
  }
};

/**
 * 初始化用户tabbar
 * 根据当前用户权限设置相应的tabbar
 */
export const initUserTabbar = () => {
  try {
    const userInfo = getUserInfo();
    console.log('初始化用户tabbar，用户信息:', userInfo);

    if (!userInfo) {
      console.log('用户未登录，使用基础tabbar');
      setDynamicTabbar(BASE_TABBAR_LIST);
      return;
    }

    const tabbarList = getTabbarByUserLevel(userInfo);
    setDynamicTabbar(tabbarList);

    // 存储当前tabbar配置到本地
    uni.setStorageSync('currentTabbar', tabbarList);
    
    console.log('用户tabbar初始化完成');
  } catch (error) {
    console.error('初始化用户tabbar失败:', error);
    // 出错时使用基础tabbar
    setDynamicTabbar(BASE_TABBAR_LIST);
  }
};

/**
 * 更新用户tabbar
 * 当用户信息变化时调用
 */
export const updateUserTabbar = () => {
  console.log('更新用户tabbar');
  initUserTabbar();
};

/**
 * 重置tabbar为默认状态
 */
export const resetTabbar = () => {
  console.log('重置tabbar为基础状态');
  setDynamicTabbar(BASE_TABBAR_LIST);
  uni.removeStorageSync('currentTabbar');
};

/**
 * 检查当前页面是否在tabbar中
 * @param {string} pagePath - 页面路径
 * @returns {boolean} 是否在tabbar中
 */
export const isTabbarPage = (pagePath) => {
  const currentTabbar = uni.getStorageSync('currentTabbar') || BASE_TABBAR_LIST;
  return currentTabbar.some(item => item.pagePath === pagePath);
};

/**
 * 获取当前tabbar配置
 * @returns {Array} 当前tabbar配置
 */
export const getCurrentTabbar = () => {
  return uni.getStorageSync('currentTabbar') || BASE_TABBAR_LIST;
};

/**
 * 检查用户是否有权限访问指定页面
 * @param {string} pagePath - 页面路径
 * @param {Object} userInfo - 用户信息
 * @returns {boolean} 是否有权限
 */
export const hasPagePermission = (pagePath, userInfo = null) => {
  const user = userInfo || getUserInfo();
  
  if (!user) {
    // 未登录用户只能访问基础页面
    return BASE_TABBAR_LIST.some(item => item.pagePath === pagePath);
  }

  // 检查特殊权限页面
  if (pagePath === 'pages/chat/chat-history' || pagePath === 'pages/admin/user-management') {
    return user.level === 1 || user.level === 2;
  }

  // 其他页面都可以访问
  return true;
};

/**
 * 安全跳转到tabbar页面
 * 检查权限后再跳转
 * @param {string} pagePath - 页面路径
 */
export const safeNavigateToTabbar = (pagePath) => {
  const userInfo = getUserInfo();
  
  if (!hasPagePermission(pagePath, userInfo)) {
    uni.showToast({
      title: '您没有权限访问此页面',
      icon: 'none'
    });
    
    // 跳转到默认页面
    uni.switchTab({
      url: '/pages/chat/ai-chat'
    });
    return;
  }

  uni.switchTab({
    url: `/${pagePath}`
  });
};
