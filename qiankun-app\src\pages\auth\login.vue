<template>
  <view class="login-container">
    <view class="login-content">
      <view class="header">
        <image class="logo" src="/static/images/logo.png" mode="aspectFit"></image>
        <text class="title">乾坤袋</text>
        <text class="subtitle">AI智能对话平台</text>
      </view>

      <view class="form-container">
        <view class="input-group">
          <view class="input-wrapper">
            <text class="input-icon">👤</text>
            <input
              class="input"
              type="text"
              placeholder="请输入用户名"
              v-model="form.username"
            />
          </view>
        </view>

        <view class="input-group">
          <view class="input-wrapper">
            <text class="input-icon">🔒</text>
            <input
              class="input"
              type="password"
              placeholder="请输入密码"
              v-model="form.password"
              password
            />
          </view>
        </view>

        <button class="login-btn" @click="handleLogin" :disabled="isLoading">
          <text v-if="!isLoading">登录</text>
          <text v-else>登录中...</text>
        </button>
      </view>

      <view class="footer">
        <text class="footer-text">© 2025 乾坤袋 All Rights Reserved</text>
      </view>
    </view>
  </view>
</template>

<script>
import { userApi } from '@/utils/api.js'

export default {
  data() {
    return {
      form: {
        username: '',
        password: ''
      },
      isLoading: false
    }
  },
  onLoad() {
    // 检查是否已登录
    const token = uni.getStorageSync('token');
    if (token) {
      this.redirectAfterLogin();
    }
  },
  methods: {
    async handleLogin() {
      // 表单验证
      if (!this.form.username) {
        uni.showToast({
          title: '请输入用户名',
          icon: 'none'
        });
        return;
      }

      if (!this.form.password) {
        uni.showToast({
          title: '请输入密码',
          icon: 'none'
        });
        return;
      }

      // 登录请求
      this.isLoading = true;

      try {
        console.log('开始登录请求，用户名:', this.form.username);

        // 调用真实的登录API
        const response = await userApi.login(this.form.username, this.form.password);

        console.log('登录响应:', response);

        // 检查响应状态
        if (response && response.code === 200 && response.data) {
          // 登录成功，保存基本信息
          const { user_id, token } = response.data;

          // 先保存token
          uni.setStorageSync('token', token);

          // 更新Vuex中的token
          if (this.$store) {
            this.$store.commit('user/SET_TOKEN', token);
          }

          // 调用获取用户详细信息接口
          await this.fetchUserDetails(user_id, token);
        } else {
          // 登录失败 - 临时模拟成功用于测试
          // this.simulateLoginSuccess();
           uni.showToast({
          title: '登录失败',
          icon: 'none'
        });
        }
      } catch (error) {
        console.error('登录错误:', error);
 uni.showToast({
          title: '登录失败',
          icon: 'none'
        });
        // 临时模拟成功用于测试
        // this.simulateLoginSuccess();
      }
    },

    // 获取用户详细信息
    async fetchUserDetails(user_id, token) {
      try {
        console.log('获取用户详细信息，用户ID:', user_id);

        // 调用获取用户信息接口
        const userResponse = await userApi.getUserInfo(user_id);

        console.log('用户信息响应:', userResponse);

        if (userResponse && userResponse.code === 200 && userResponse.data && userResponse.data.length > 0) {
          const userDetail = userResponse.data[0]; // 取第一条记录

          // 根据用户级别设置角色
          let role = 'user';
          switch (userDetail.level) {
            case 1:
              role = 'admin';
              break;
            case 2:
              role = 'manager';
              break;
            default:
              role = 'user';
          }

          // 构建完整的用户信息
          const userInfo = {
            id: userDetail.id,
            username: this.form.username,
            name: userDetail.name || this.form.username,
            level: userDetail.level,
            picture: userDetail.picture,
            notice: userDetail.notice,
            role: role
          };

          // 保存到本地存储
          uni.setStorageSync('userInfo', userInfo);

          // 更新Vuex状态
          if (this.$store) {
            this.$store.commit('user/SET_USER_INFO', userInfo);
            console.log('已更新Vuex用户状态');
          }

          // 初始化用户tabbar
          const { initUserTabbar } = await import('@/utils/tabbar.js');
          initUserTabbar();

          this.isLoading = false;

          uni.showToast({
            title: '登录成功',
            icon: 'success',
            duration: 1500,
            success: () => {
              setTimeout(() => {
                this.redirectAfterLogin();
              }, 1500);
            }
          });
        } else {
          // 获取用户信息失败，但登录成功，使用基本信息
          console.warn('获取用户详细信息失败，使用基本信息');
          this.saveBasicUserInfo(user_id);
        }
      } catch (error) {
        console.error('获取用户详细信息错误:', error);
        // 获取用户信息失败，但登录成功，使用基本信息
        this.saveBasicUserInfo(user_id);
      }
    },

    // 保存基本用户信息（当获取详细信息失败时使用）
    saveBasicUserInfo(user_id) {
      const userInfo = {
        id: user_id,
        username: this.form.username,
        name: this.form.username,
        level: 3, // 默认为普通用户
        picture: '',
        notice: '',
        role: 'user'
      };

      // 保存到本地存储
      uni.setStorageSync('userInfo', userInfo);

      // 更新Vuex状态
      if (this.$store) {
        this.$store.commit('user/SET_USER_INFO', userInfo);
        console.log('已更新Vuex用户状态（基本信息）');
      }

      // 初始化用户tabbar
      const { initUserTabbar } = await import('@/utils/tabbar.js');
      initUserTabbar();

      this.isLoading = false;

      uni.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 1500,
        success: () => {
          setTimeout(() => {
            this.redirectAfterLogin();
          }, 1500);
        }
      });
    },

    redirectAfterLogin() {
      // 跳转到AI对话页面
      uni.switchTab({
        url: '/pages/chat/ai-chat'
      });
    },

    // 临时模拟登录成功的方法（用于测试）
    simulateLoginSuccess() {
      console.log('模拟登录成功');

      // 模拟API返回的数据
      const mockToken = 'mock-token-' + Date.now();
      const mockUserId = 'mock-user-' + Date.now();

      // 根据用户名判断角色和级别
      let role = 'user';
      let name = '普通用户';
      let level = 3;

      if (this.form.username === 'admin') {
        role = 'admin';
        name = '超级管理员';
        level = 1;
      } else if (this.form.username === 'manager') {
        role = 'manager';
        name = '客户经理';
        level = 2;
      }

      // 构建用户信息
      const userInfo = {
        id: mockUserId,
        username: this.form.username,
        name: name,
        level: level,
        picture: 'https://via.placeholder.com/100x100?text=' + encodeURIComponent(name),
        notice: '这是一个模拟的用户通知信息',
        role: role
      };

      // 存储模拟的完整用户信息和token
      uni.setStorageSync('token', mockToken);
      uni.setStorageSync('userInfo', userInfo);

      // 更新Vuex中的token
      if (this.$store) {
        this.$store.commit('user/SET_TOKEN', mockToken);
      }

      // 更新Vuex状态
      if (this.$store) {
        this.$store.commit('user/SET_USER_INFO', userInfo);
        console.log('已更新Vuex用户状态（模拟登录）');
      }

      // 初始化用户tabbar
      const { initUserTabbar } = await import('@/utils/tabbar.js');
      initUserTabbar();

      this.isLoading = false;

      uni.showToast({
        title: '登录成功（模拟）',
        icon: 'success',
        duration: 1500,
        success: () => {
          setTimeout(() => {
            this.redirectAfterLogin();
          }, 1500);
        }
      });
    },


  }
}
</script>

<style>
.login-container {
  height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #1976D2, #64B5F6);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0 40rpx;
  box-sizing: border-box;
}

.login-content {
  width: 100%;
  max-width: 750rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 30rpx;
}

.title {
  font-size: 60rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.subtitle {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.9);
}

.form-container {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 50rpx 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
}

.input-group {
  margin-bottom: 30rpx;
}

.input-wrapper {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e5e5e5;
  padding: 15rpx 0;
}

.input-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  color: #666;
}

.input {
  flex: 1;
  height: 80rpx;
  font-size: 32rpx;
  color: #333;
}

.login-btn {
  height: 90rpx;
  line-height: 90rpx;
  background: linear-gradient(to right, #1976D2, #64B5F6);
  color: #fff;
  border-radius: 45rpx;
  font-size: 32rpx;
  margin-top: 20rpx;
  box-shadow: 0 5rpx 15rpx rgba(25, 118, 210, 0.3);
  border: none;
}

.login-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 3rpx 10rpx rgba(25, 118, 210, 0.2);
}

.footer {
  margin-top: 60rpx;
  text-align: center;
}

.footer-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}
</style>
