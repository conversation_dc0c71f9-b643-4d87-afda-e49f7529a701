<template>
  <view class="login-container">
    <view class="login-content">
      <view class="header">
        <image class="logo" src="/static/images/logo.png" mode="aspectFit"></image>
        <text class="title">乾坤袋</text>
        <text class="subtitle">AI智能对话平台</text>
      </view>

      <view class="form-container">
        <view class="input-group">
          <view class="input-wrapper">
            <text class="input-icon">👤</text>
            <input
              class="input"
              type="text"
              placeholder="请输入用户名"
              v-model="form.username"
            />
          </view>
        </view>

        <view class="input-group">
          <view class="input-wrapper">
            <text class="input-icon">🔒</text>
            <input
              class="input"
              type="password"
              placeholder="请输入密码"
              v-model="form.password"
              password
            />
          </view>
        </view>

        <button class="login-btn" @click="handleLogin" :disabled="isLoading">
          <text v-if="!isLoading">登录</text>
          <text v-else>登录中...</text>
        </button>
      </view>

      <view class="footer">
        <text class="footer-text">© 2025 乾坤袋 All Rights Reserved</text>
      </view>
    </view>
  </view>
</template>

<script>
import { userApi } from '@/utils/api.js'

export default {
  data() {
    return {
      form: {
        username: '',
        password: ''
      },
      isLoading: false
    }
  },
  onLoad() {
    // 检查是否已登录
    const token = uni.getStorageSync('token');
    if (token) {
      this.redirectAfterLogin();
    }
  },
  methods: {
    async handleLogin() {
      // 表单验证
      if (!this.form.username) {
        uni.showToast({
          title: '请输入用户名',
          icon: 'none'
        });
        return;
      }

      if (!this.form.password) {
        uni.showToast({
          title: '请输入密码',
          icon: 'none'
        });
        return;
      }

      // 登录请求
      this.isLoading = true;

      try {
        console.log('开始登录请求，用户名:', this.form.username);

        // 调用真实的登录API
        const response = await userApi.login(this.form.username, this.form.password);

        console.log('登录响应:', response);

        // 检查响应状态
        if (response && response.code === 200 && response.data) {
          // 登录成功，保存用户信息和token
          const { user_id, token } = response.data;

          // 根据用户级别设置角色
          let role = 'user';
          let name = this.form.username;

          // 这里可以根据返回的用户信息设置角色
          // 如果API返回了用户级别信息，可以在这里处理

          uni.setStorageSync('token', token);
          uni.setStorageSync('userInfo', {
            id: user_id,
            username: this.form.username,
            name: name,
            role: role
          });

          this.isLoading = false;

          uni.showToast({
            title: '登录成功',
            icon: 'success',
            duration: 1500,
            success: () => {
              setTimeout(() => {
                this.redirectAfterLogin();
              }, 1500);
            }
          });
        } else {
          // 登录失败
          this.isLoading = false;

          let errorMessage = '登录失败';
          if (response && response.code) {
            switch (response.code) {
              case 401:
                errorMessage = '用户名或密码错误';
                break;
              case 500:
                errorMessage = '服务器内部错误';
                break;
              default:
                errorMessage = response.message || '登录失败';
            }
          }

          uni.showToast({
            title: errorMessage,
            icon: 'none'
          });
        }
      } catch (error) {
        this.isLoading = false;
        console.error('登录错误:', error);

        let errorMessage = '网络错误，请稍后重试';

        // 根据错误类型提供更具体的错误信息
        if (error.message) {
          if (error.message.includes('用户名和密码不能为空')) {
            errorMessage = '用户名和密码不能为空';
          } else if (error.message.includes('cors')) {
            errorMessage = '网络连接问题，请检查网络设置';
          } else if (error.message.includes('timeout')) {
            errorMessage = '请求超时，请稍后重试';
          } else {
            errorMessage = error.message;
          }
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        });
      }
    },

    redirectAfterLogin() {
      // 跳转到AI对话页面
      uni.switchTab({
        url: '/pages/chat/ai-chat'
      });
    },


  }
}
</script>

<style>
.login-container {
  height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #1976D2, #64B5F6);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0 40rpx;
  box-sizing: border-box;
}

.login-content {
  width: 100%;
  max-width: 750rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 30rpx;
}

.title {
  font-size: 60rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.subtitle {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.9);
}

.form-container {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 50rpx 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
}

.input-group {
  margin-bottom: 30rpx;
}

.input-wrapper {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e5e5e5;
  padding: 15rpx 0;
}

.input-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  color: #666;
}

.input {
  flex: 1;
  height: 80rpx;
  font-size: 32rpx;
  color: #333;
}

.login-btn {
  height: 90rpx;
  line-height: 90rpx;
  background: linear-gradient(to right, #1976D2, #64B5F6);
  color: #fff;
  border-radius: 45rpx;
  font-size: 32rpx;
  margin-top: 20rpx;
  box-shadow: 0 5rpx 15rpx rgba(25, 118, 210, 0.3);
  border: none;
}

.login-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 3rpx 10rpx rgba(25, 118, 210, 0.2);
}

.footer {
  margin-top: 60rpx;
  text-align: center;
}

.footer-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}
</style>
