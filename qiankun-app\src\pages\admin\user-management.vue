<template>
  <view class="user-management-container">
    <view class="search-bar">
      <input 
        class="search-input" 
        type="text" 
        placeholder="搜索用户名..." 
        v-model="searchKeyword"
        @input="handleSearch"
      />
      <text class="search-icon">🔍</text>
    </view>
    
    <view class="action-bar">
      <button class="add-btn" @click="navigateToCreateUser">
        <text class="btn-text">创建用户</text>
      </button>
    </view>
    
    <view class="user-list">
      <view 
        class="user-item" 
        v-for="(user, index) in filteredUsers" 
        :key="index"
        @click="showUserActions(user)"
      >
        <view class="user-avatar" :class="{ 'admin': user.role === 'admin', 'manager': user.role === 'manager' }">
          <text class="avatar-text">{{ user.name.substring(0, 1) }}</text>
        </view>
        
        <view class="user-info">
          <view class="user-header">
            <text class="user-name">{{ user.name }}</text>
            <text class="user-role" :class="{ 'admin': user.role === 'admin', 'manager': user.role === 'manager' }">
              {{ getRoleName(user.role) }}
            </text>
          </view>
          
          <text class="user-username">用户名: {{ user.username }}</text>
        </view>
      </view>
      
      <view class="empty-state" v-if="filteredUsers.length === 0">
        <image class="empty-icon" src="/static/images/empty-user.png"></image>
        <text class="empty-text">暂无用户</text>
        <text class="empty-subtext">点击"创建用户"按钮添加新用户</text>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex';

export default {
  data() {
    return {
      users: [],
      searchKeyword: '',
      isLoading: false
    }
  },
  computed: {
    ...mapState('user', ['userInfo']),
    
    filteredUsers() {
      if (!this.searchKeyword) {
        return this.users;
      }
      
      return this.users.filter(user => {
        return user.name.toLowerCase().includes(this.searchKeyword.toLowerCase()) || 
               user.username.toLowerCase().includes(this.searchKeyword.toLowerCase());
      });
    }
  },
  onLoad() {
    // 检查是否已登录
    const token = uni.getStorageSync('token');
    if (!token) {
      uni.redirectTo({
        url: '/pages/auth/login'
      });
      return;
    }
    
    // 检查用户权限
    const userInfo = uni.getStorageSync('userInfo');
    if (userInfo.role === 'user') {
      uni.showToast({
        title: '您没有权限访问此页面',
        icon: 'none'
      });
      
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/chat/ai-chat'
        });
      }, 1500);
      
      return;
    }
    
    // 加载用户列表
    this.loadUsers();
  },
  onShow() {
    // 每次页面显示时刷新用户列表
    this.loadUsers();
  },
  methods: {
    loadUsers() {
      this.isLoading = true;
      
      // 模拟加载用户列表
      setTimeout(() => {
        // 这里应该是实际的API调用
        
        // 模拟用户数据
        let users = [
          {
            id: '1',
            username: 'admin',
            name: '超级管理员',
            role: 'admin'
          },
          {
            id: '2',
            username: 'manager',
            name: '客户经理',
            role: 'manager'
          },
          {
            id: '3',
            username: 'user1',
            name: '普通用户1',
            role: 'user'
          },
          {
            id: '4',
            username: 'user2',
            name: '普通用户2',
            role: 'user'
          }
        ];
        
        // 根据当前用户角色过滤
        if (this.userInfo.role === 'manager') {
          // 客户经理只能看到自己创建的普通用户
          users = users.filter(u => u.role === 'user');
        }
        
        this.users = users;
        this.isLoading = false;
      }, 500);
    },
    
    handleSearch() {
      // 搜索功能已通过计算属性实现
    },
    
    navigateToCreateUser() {
      uni.navigateTo({
        url: '/pages/admin/create-user'
      });
    },
    
    showUserActions(user) {
      // 不能对自己或更高权限的用户执行操作
      if (user.username === this.userInfo.username || 
          (this.userInfo.role === 'manager' && user.role !== 'user')) {
        return;
      }
      
      uni.showActionSheet({
        itemList: ['编辑', '删除'],
        success: res => {
          if (res.tapIndex === 0) {
            // 编辑用户
            uni.navigateTo({
              url: `/pages/admin/edit-user?id=${user.id}`
            });
          } else if (res.tapIndex === 1) {
            // 删除用户
            this.confirmDeleteUser(user);
          }
        }
      });
    },
    
    confirmDeleteUser(user) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除用户"${user.name}"吗？`,
        success: res => {
          if (res.confirm) {
            this.deleteUser(user.id);
          }
        }
      });
    },
    
    deleteUser(userId) {
      // 模拟删除用户
      this.users = this.users.filter(user => user.id !== userId);
      
      uni.showToast({
        title: '删除成功',
        icon: 'success'
      });
    },
    
    getRoleName(role) {
      switch (role) {
        case 'admin':
          return '超级管理员';
        case 'manager':
          return '客户经理';
        case 'user':
          return '普通用户';
        default:
          return '未知角色';
      }
    }
  }
}
</script>

<style>
.user-management-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
}

.search-bar {
  padding: 20rpx;
  background-color: #fff;
  position: relative;
}

.search-input {
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  padding: 0 80rpx 0 30rpx;
  font-size: 28rpx;
  width: 100%;
  box-sizing: border-box;
}

.search-icon {
  position: absolute;
  right: 40rpx;
  top: 40rpx;
  font-size: 32rpx;
  color: #999;
}

.action-bar {
  padding: 20rpx;
  display: flex;
  justify-content: flex-end;
}

.add-btn {
  background-color: #007AFF;
  color: #fff;
  border-radius: 10rpx;
  font-size: 28rpx;
  padding: 0 30rpx;
  height: 80rpx;
  line-height: 80rpx;
}

.btn-text {
  color: #fff;
}

.user-list {
  flex: 1;
  padding: 0 0 20rpx 0;
}

.user-item {
  display: flex;
  padding: 30rpx;
  background-color: #fff;
  border-bottom: 1px solid #f5f5f5;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #4CD964;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.user-avatar.admin {
  background-color: #FF9500;
}

.user-avatar.manager {
  background-color: #007AFF;
}

.avatar-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

.user-info {
  flex: 1;
}

.user-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.user-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.user-role {
  font-size: 24rpx;
  color: #4CD964;
  background-color: rgba(76, 217, 100, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.user-role.admin {
  color: #FF9500;
  background-color: rgba(255, 149, 0, 0.1);
}

.user-role.manager {
  color: #007AFF;
  background-color: rgba(0, 122, 255, 0.1);
}

.user-username {
  font-size: 28rpx;
  color: #666;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.empty-subtext {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}
</style>
