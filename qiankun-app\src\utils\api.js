/**
 * API 请求工具类
 */

// API 基础配置
const API_BASE_URL = 'http://114.55.38.233:11080'; // 根据您的接口文档设置

// 通用请求方法
const request = (url, options = {}) => {
  return new Promise((resolve, reject) => {
    // 获取当前平台
    const platform = uni.getSystemInfoSync().platform;

    // 构建完整URL
    let fullUrl = url;
    if (!url.startsWith('http')) {
      // 小程序环境使用完整URL，H5环境可能需要代理
      if (platform === 'devtools' || platform === 'android' || platform === 'ios') {
        fullUrl = `${API_BASE_URL}${url}`;
      } else {
        // H5环境，假设有代理配置
        fullUrl = url;
      }
    }

    const defaultOptions = {
      method: 'GET',
      timeout: 30000,
      header: {
        'Content-Type': 'application/json'
      }
    };

    const requestOptions = {
      ...defaultOptions,
      ...options,
      url: fullUrl,
      header: {
        ...defaultOptions.header,
        ...options.header
      }
    };

    uni.request({
      ...requestOptions,
      success: (res) => {
        console.log('API请求成功:', res);
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data);
        } else {
          reject(new Error(`HTTP ${res.statusCode}: ${res.data?.message || '请求失败'}`));
        }
      },
      fail: (err) => {
        console.error('API请求失败:', err);
        reject(err);
      }
    });
  });
};

// 云函数调用
export const callCloudFunction = async (name, data = {}) => {
  try {
    const result = await uniCloud.callFunction({
      name,
      data
    });

    return result.result;
  } catch (error) {
    console.error(`调用云函数 ${name} 失败:`, error);
    throw error;
  }
};

// 用户相关 API
export const userApi = {
  // 用户注册
  register: (data) => callCloudFunction('register', data),

  // 用户登录 - 使用新的API接口
  login: async (username, password) => {
    try {
      // 验证参数
      if (!username || !password) {
        throw new Error('用户名和密码不能为空');
      }

      // 构建查询参数
      const queryParams = `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`;

      const response = await request(`/login/?${queryParams}`, {
        method: 'GET'
      });

      // 处理响应数据
      if (response && typeof response === 'object') {
        return response;
      } else {
        throw new Error('服务器响应格式错误');
      }
    } catch (error) {
      console.error('登录请求失败:', error);
      throw error;
    }
  },

  // 云函数登录（备用）
  loginWithCloudFunction: (data) => callCloudFunction('login', data),

  // 获取用户信息
  getUserInfo: async (user_id) => {
    try {
      // 验证参数
      if (!user_id) {
        throw new Error('用户ID不能为空');
      }

      // 获取token
      const token = uni.getStorageSync('token');
      if (!token) {
        throw new Error('未登录，请先登录');
      }

      // 构建查询参数
      const queryParams = `user_id=${encodeURIComponent(user_id)}`;

      const response = await request(`/GetUser/?${queryParams}`, {
        method: 'GET',
        header: {
          'Authorization': `Bearer ${token}`
        }
      });

      // 处理响应数据
      if (response && typeof response === 'object') {
        return response;
      } else {
        throw new Error('服务器响应格式错误');
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  },

  // 新增用户
  addUser: async (userData) => {
    console.log('addUser API 方法被调用，参数:', userData);

    try {
      // 验证参数
      if (!userData.username || !userData.password) {
        console.error('参数验证失败: 用户名或密码为空');
        throw new Error('用户名和密码不能为空');
      }

      console.log('参数验证通过');

      // 获取token
      const token = uni.getStorageSync('token');
      console.log('获取到的token:', token);

      if (!token) {
        console.error('token为空，未登录');
        throw new Error('未登录，请先登录');
      }

      // 检查是否为模拟token，如果是则返回模拟成功响应
      if (token.startsWith('mock-token-')) {
        console.log('检测到模拟token，使用模拟响应');
        const mockResponse = {
          code: 200,
          message: '用户创建成功（模拟）',
          data: {
            user_id: 'mock-user-' + Date.now()
          }
        };
        console.log('返回模拟响应:', mockResponse);
        return mockResponse;
      }

      // 构建查询参数
      const params = [];
      params.push(`username=${encodeURIComponent(userData.username)}`);
      params.push(`notice=${encodeURIComponent(userData.notice || 1)}`);
      params.push(`picture=${encodeURIComponent(userData.picture || '')}`);
      params.push(`phone=${encodeURIComponent(userData.phone || '')}`);
      params.push(`father_id=${encodeURIComponent(userData.father_id || '')}`);
      params.push(`password=${encodeURIComponent(userData.password)}`);
      params.push(`user_permission=${encodeURIComponent(userData.user_permission || 1)}`);
      const queryParams = params.join('&');

      const response = await request(`/AddUser/?${queryParams}`, {
        method: 'PUT',
        header: {
          'Authorization': `Bearer ${token}`
        }
      });

      // 处理响应数据
      if (response && typeof response === 'object') {
        return response;
      } else {
        throw new Error('服务器响应格式错误');
      }
    } catch (error) {
      console.error('新增用户失败:', error);
      throw error;
    }
  },

  // 获取对话内容
  getTalks: async (user_id) => {
    try {
      // 验证参数
      if (!user_id) {
        throw new Error('用户ID不能为空');
      }

      // 获取token
      const token = uni.getStorageSync('token');
      if (!token) {
        throw new Error('未登录，请先登录');
      }

      console.log('获取对话内容，用户ID:', user_id);

      // 检查是否为模拟token，如果是则返回模拟数据
      if (token.startsWith('mock-token-')) {
        console.log('使用模拟对话数据');
        return {
          code: 200,
          data: [
            {
              id: 'user1',
              talk_id: 'talk1',
              question: '你好，请问如何使用这个系统？',
              answer: '您好！欢迎使用我们的系统。您可以通过以下步骤开始使用...',
              name: '张三',
              level: 3,
              picture: 'https://via.placeholder.com/50x50?text=张',
              date: '2024-01-15 10:30:00'
            },
            {
              id: 'user1',
              talk_id: 'talk2',
              question: '系统有哪些主要功能？',
              answer: '我们的系统主要包含以下功能：1. AI智能对话 2. 用户管理 3. 历史记录查看...',
              name: '张三',
              level: 3,
              picture: 'https://via.placeholder.com/50x50?text=张',
              date: '2024-01-15 10:35:00'
            },
            {
              id: 'user2',
              talk_id: 'talk3',
              question: '如何修改个人信息？',
              answer: '您可以在个人中心页面修改您的个人信息，包括头像、密码等...',
              name: '李四',
              level: 3,
              picture: 'https://via.placeholder.com/50x50?text=李',
              date: '2024-01-15 11:00:00'
            },
            {
              id: 'user3',
              talk_id: 'talk4',
              question: '系统支持哪些平台？',
              answer: '我们的系统支持微信小程序、H5网页版，以及iOS和Android应用...',
              name: '王五',
              level: 2,
              picture: 'https://via.placeholder.com/50x50?text=王',
              date: '2024-01-15 14:20:00'
            }
          ]
        };
      }

      // 构建查询参数
      const queryParams = `user_id=${encodeURIComponent(user_id)}`;

      const response = await request(`/GetTalks/?${queryParams}`, {
        method: 'GET',
        header: {
          'Authorization': `Bearer ${token}`
        }
      });

      // 处理响应数据
      if (response && typeof response === 'object') {
        return response;
      } else {
        throw new Error('服务器响应格式错误');
      }
    } catch (error) {
      console.error('获取对话内容失败:', error);
      throw error;
    }
  },

  // 退出登录
  logout: async () => {
    try {
      // 获取token
      const token = uni.getStorageSync('token');
      if (!token) {
        console.log('没有token，无需调用退出接口');
        return { code: 200, message: '退出成功' };
      }

      console.log('开始调用退出登录接口，token:', token);

      // 检查是否为模拟token，如果是则返回模拟成功响应
      if (token.startsWith('mock-token-')) {
        console.log('检测到模拟token，使用模拟退出响应');
        return {
          code: 200,
          message: '退出登录成功（模拟）'
        };
      }

      const response = await request('/logout/', {
        method: 'POST',
        header: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      // 处理响应数据
      if (response && typeof response === 'object') {
        return response;
      } else {
        throw new Error('服务器响应格式错误');
      }
    } catch (error) {
      console.error('退出登录失败:', error);
      // 即使接口调用失败，也要清空本地缓存
      return { code: 200, message: '退出登录' };
    }
  },

  // 修改密码
  changePassword: async (user_id, oldPassword, newPassword) => {
    try {
      // 验证参数
      if (!user_id || !oldPassword || !newPassword) {
        throw new Error('用户ID、原密码和新密码不能为空');
      }

      // 获取token
      const token = uni.getStorageSync('token');
      if (!token) {
        throw new Error('未登录，请先登录');
      }

      // 检查是否为模拟token，如果是则返回模拟成功响应
      if (token.startsWith('mock-token-')) {
        console.log('使用模拟修改密码响应');
        return {
          code: 200,
          message: '密码修改成功（模拟）'
        };
      }

      // 构建查询参数（根据接口文档，参数直接传递）
      const queryParams = `user_id=${encodeURIComponent(user_id)}&password=${encodeURIComponent(newPassword)}`;

      const response = await request(`/ChangePassword/?${queryParams}`, {
        method: 'PUT',
        header: {
          'Authorization': `Bearer ${token}`
        }
      });

      // 处理响应数据
      if (response && typeof response === 'object') {
        return response;
      } else {
        throw new Error('服务器响应格式错误');
      }
    } catch (error) {
      console.error('修改密码失败:', error);
      throw error;
    }
  },

  // 实名认证
  verifyRealName: (data) => callCloudFunction('verifyRealName', data),

  // 绑定业务人员
  bindBusinessPersonnel: (data) => callCloudFunction('bindBusinessPersonnel', data)
};

// 消息相关 API
export const messageApi = {
  // 获取聊天列表
  getChatList: (userId) => callCloudFunction('getChatList', { userId }),
  
  // 获取聊天消息
  getMessages: (data) => callCloudFunction('getMessages', data),
  
  // 发送消息
  sendMessage: (data) => callCloudFunction('sendMessage', data),
  
  // 标记消息为已读
  markMessagesAsRead: (data) => callCloudFunction('markMessagesAsRead', data)
};

// 业务人员相关 API
export const businessPersonnelApi = {
  // 获取业务人员信息
  getBusinessPersonnel: (id) => callCloudFunction('getBusinessPersonnel', { id }),
  
  // 获取业务人员二维码
  getBusinessPersonnelQRCode: (id) => callCloudFunction('getBusinessPersonnelQRCode', { id })
};
