/**
 * API 请求工具类
 */

// API 基础配置
const API_BASE_URL = 'http://114.55.38.233:11080'; // 根据您的接口文档设置

// 通用请求方法
const request = (url, options = {}) => {
  return new Promise((resolve, reject) => {
    // 获取当前平台
    const platform = uni.getSystemInfoSync().platform;

    // 构建完整URL
    let fullUrl = url;
    if (!url.startsWith('http')) {
      // 小程序环境使用完整URL，H5环境可能需要代理
      if (platform === 'devtools' || platform === 'android' || platform === 'ios') {
        fullUrl = `${API_BASE_URL}${url}`;
      } else {
        // H5环境，假设有代理配置
        fullUrl = url;
      }
    }

    const defaultOptions = {
      method: 'GET',
      timeout: 30000,
      header: {
        'Content-Type': 'application/json'
      }
    };

    const requestOptions = {
      ...defaultOptions,
      ...options,
      url: fullUrl,
      header: {
        ...defaultOptions.header,
        ...options.header
      }
    };

    uni.request({
      ...requestOptions,
      success: (res) => {
        console.log('API请求成功:', res);
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data);
        } else {
          reject(new Error(`HTTP ${res.statusCode}: ${res.data?.message || '请求失败'}`));
        }
      },
      fail: (err) => {
        console.error('API请求失败:', err);
        reject(err);
      }
    });
  });
};

// 云函数调用
export const callCloudFunction = async (name, data = {}) => {
  try {
    const result = await uniCloud.callFunction({
      name,
      data
    });

    return result.result;
  } catch (error) {
    console.error(`调用云函数 ${name} 失败:`, error);
    throw error;
  }
};

// 用户相关 API
export const userApi = {
  // 用户注册
  register: (data) => callCloudFunction('register', data),

  // 用户登录 - 使用新的API接口
  login: async (username, password) => {
    try {
      // 验证参数
      if (!username || !password) {
        throw new Error('用户名和密码不能为空');
      }

      // 构建查询参数
      const queryParams = new URLSearchParams({
        username: username,
        password: password
      }).toString();

      const response = await request(`/login/?${queryParams}`, {
        method: 'GET'
      });

      // 处理响应数据
      if (response && typeof response === 'object') {
        return response;
      } else {
        throw new Error('服务器响应格式错误');
      }
    } catch (error) {
      console.error('登录请求失败:', error);
      throw error;
    }
  },

  // 云函数登录（备用）
  loginWithCloudFunction: (data) => callCloudFunction('login', data),

  // 实名认证
  verifyRealName: (data) => callCloudFunction('verifyRealName', data),

  // 绑定业务人员
  bindBusinessPersonnel: (data) => callCloudFunction('bindBusinessPersonnel', data)
};

// 消息相关 API
export const messageApi = {
  // 获取聊天列表
  getChatList: (userId) => callCloudFunction('getChatList', { userId }),
  
  // 获取聊天消息
  getMessages: (data) => callCloudFunction('getMessages', data),
  
  // 发送消息
  sendMessage: (data) => callCloudFunction('sendMessage', data),
  
  // 标记消息为已读
  markMessagesAsRead: (data) => callCloudFunction('markMessagesAsRead', data)
};

// 业务人员相关 API
export const businessPersonnelApi = {
  // 获取业务人员信息
  getBusinessPersonnel: (id) => callCloudFunction('getBusinessPersonnel', { id }),
  
  // 获取业务人员二维码
  getBusinessPersonnelQRCode: (id) => callCloudFunction('getBusinessPersonnelQRCode', { id })
};
