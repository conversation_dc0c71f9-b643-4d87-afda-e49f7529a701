/**
 * API 请求工具类
 */

// 云函数调用
export const callCloudFunction = async (name, data = {}) => {
  try {
    const result = await uniCloud.callFunction({
      name,
      data
    });
    
    return result.result;
  } catch (error) {
    console.error(`调用云函数 ${name} 失败:`, error);
    throw error;
  }
};

// 用户相关 API
export const userApi = {
  // 用户注册
  register: (data) => callCloudFunction('register', data),
  
  // 用户登录
  login: (data) => callCloudFunction('login', data),
  
  // 实名认证
  verifyRealName: (data) => callCloudFunction('verifyRealName', data),
  
  // 绑定业务人员
  bindBusinessPersonnel: (data) => callCloudFunction('bindBusinessPersonnel', data)
};

// 消息相关 API
export const messageApi = {
  // 获取聊天列表
  getChatList: (userId) => callCloudFunction('getChatList', { userId }),
  
  // 获取聊天消息
  getMessages: (data) => callCloudFunction('getMessages', data),
  
  // 发送消息
  sendMessage: (data) => callCloudFunction('sendMessage', data),
  
  // 标记消息为已读
  markMessagesAsRead: (data) => callCloudFunction('markMessagesAsRead', data)
};

// 业务人员相关 API
export const businessPersonnelApi = {
  // 获取业务人员信息
  getBusinessPersonnel: (id) => callCloudFunction('getBusinessPersonnel', { id }),
  
  // 获取业务人员二维码
  getBusinessPersonnelQRCode: (id) => callCloudFunction('getBusinessPersonnelQRCode', { id })
};
